# 结直肠癌筛查微观模拟模型 (CCSM) 用户手册

## 目录

1. [简介](#简介)
2. [安装指南](#安装指南)
3. [快速开始](#快速开始)
4. [模型架构](#模型架构)
5. [使用指南](#使用指南)
6. [API参考](#api参考)
7. [示例](#示例)
8. [常见问题](#常见问题)
9. [故障排除](#故障排除)

## 简介

结直肠癌筛查微观模拟模型 (CCSM) 是一个基于Python的微观模拟模型，用于评估不同结直肠癌筛查策略的成本效益。该模型模拟个体从正常状态到腺瘤发生、癌症发展和死亡的整个自然史过程，并评估各种筛查策略的健康和经济学效果。

### 主要特性

- **微观模拟**: 跟踪每个个体的疾病进展过程
- **多种筛查工具**: 支持FIT、结肠镜、乙状结肠镜等筛查方法
- **经济学评价**: 计算成本效益比、QALYs等指标
- **机器学习校准**: 使用深度神经网络自动校准模型参数
- **灵活的策略配置**: 支持自定义筛查策略
- **数据管理**: 完整的数据导入导出和结果存储功能

### 适用场景

- 卫生政策制定者评估筛查策略
- 卫生经济学研究
- 临床决策支持
- 学术研究和教学

## 安装指南

### 系统要求

- Python 3.8 或更高版本
- 操作系统：Windows、macOS、Linux
- 内存：建议4GB以上
- 存储空间：至少1GB可用空间

### 安装步骤

#### 方法1：使用pip安装（推荐）

```bash
pip install ccsm
```

#### 方法2：从源码安装

```bash
# 克隆仓库
git clone https://github.com/your-repo/ccsm.git
cd ccsm

# 安装依赖
pip install -r requirements.txt

# 安装包
pip install -e .
```

### 验证安装

```python
import ccsm
print(ccsm.__version__)
```

## 快速开始

### 基本使用

```python
from ccsm.core.model import ColorectalCancerMicrosimulationModel

# 创建模型实例
model = ColorectalCancerMicrosimulationModel(initial_population=1000)

# 设置人口分布
age_distribution = {50: 0.3, 55: 0.4, 60: 0.3}
model.setup_population(age_distribution, gender_ratio=0.5)

# 运行模拟
results = model.run_simulation(years=10, screening_strategy="annual_fit")

# 查看结果
print(f"总成本: ¥{results['economics_outcome']['total_cost']:,.0f}")
print(f"QALYs: {results['economics_outcome']['qalys_gained']:.2f}")
```

### 命令行使用

```bash
# 运行基本模拟
ccsm run --population 10000 --years 20 --strategy annual_fit

# 比较多个策略
ccsm compare --population 5000 --strategies annual_fit biennial_fit colonoscopy_10y

# 执行校准
ccsm calibrate --samples 10000 --method neural_network

# 导出结果
ccsm export --format csv --output results.csv --type economics
```

## 模型架构

### 核心组件

1. **个体模块 (Individual)**: 定义个体属性和状态
2. **人口模块 (Population)**: 管理人口生成和更新
3. **疾病模块 (Disease)**: 模拟疾病自然史
4. **筛查模块 (Screening)**: 实现筛查策略和工具
5. **经济学模块 (Economics)**: 计算成本效益指标
6. **校准模块 (Calibration)**: 参数校准和验证
7. **数据管理模块 (Data)**: 数据存储和管理

### 疾病自然史

模型实现了两个主要的癌变通路：

1. **腺瘤-癌通路**: 正常 → 低风险腺瘤 → 高风险腺瘤 → 临床前癌症 → 临床癌症
2. **锯齿状通路**: 正常 → 锯齿状腺瘤 → 临床前癌症 → 临床癌症

### 筛查策略

支持的筛查工具：
- **FIT (粪便免疫化学试验)**: 年度或双年度
- **结肠镜检查**: 10年间隔
- **乙状结肠镜**: 5年间隔
- **风险问卷**: 风险分层工具

## 使用指南

### 创建和配置模型

```python
from ccsm.core.model import ColorectalCancerMicrosimulationModel, ModelConfiguration
from ccsm.modules.population import PopulationParameters
from ccsm.modules.disease import DiseaseParameters

# 自定义配置
config = ModelConfiguration(
    initial_population=5000,
    simulation_years=15,
    start_year=2025,
    random_seed=42
)

# 自定义人口参数
pop_params = PopulationParameters(
    max_age=100,
    start_year=2025
)

# 创建模型
model = ColorectalCancerMicrosimulationModel(config=config)
```

### 设置人口分布

```python
# 年龄分布（年龄: 比例）
age_distribution = {
    45: 0.1,
    50: 0.2,
    55: 0.3,
    60: 0.2,
    65: 0.15,
    70: 0.05
}

# 性别比例（男性比例）
gender_ratio = 0.52

model.setup_population(age_distribution, gender_ratio)
```

### 自定义筛查策略

```python
from ccsm.modules.screening import ScreeningStrategy
from ccsm.core.enums import ScreeningTool

# 创建自定义策略
custom_strategy = ScreeningStrategy(
    name="hybrid_strategy",
    start_age=50,
    end_age=75,
    tools=[ScreeningTool.FIT, ScreeningTool.COLONOSCOPY],
    intervals=[1, 10],  # FIT每年，结肠镜每10年
    sequential=True,    # 顺序执行
    description="FIT筛查结合结肠镜检查"
)

# 添加到模型
model.add_screening_strategy(custom_strategy)
```

### 运行模拟和分析

```python
# 单策略模拟
results = model.run_simulation(
    years=20,
    screening_strategy="hybrid_strategy",
    show_progress=True
)

# 多策略比较
comparison = model.compare_strategies(
    strategy_names=["annual_fit", "biennial_fit", "hybrid_strategy"],
    years=20
)

# 查看推荐
for recommendation in comparison['recommendations']:
    print(recommendation)
```

### 经济学分析

```python
from ccsm.modules.economics import EconomicsModule, EconomicParameters

# 自定义经济学参数
econ_params = EconomicParameters(
    discount_rate=0.03,
    willingness_to_pay_threshold=150000,  # 15万元/QALY
    screening_costs={
        ScreeningTool.FIT: 60.0,
        ScreeningTool.COLONOSCOPY: 1200.0
    }
)

# 创建经济学模块
economics = EconomicsModule(econ_params)

# 评估策略
outcome = economics.evaluate_strategy(
    "annual_fit",
    model.population_module.population,
    20
)

print(f"成本效益比: ¥{outcome.cost_per_qaly_gained:,.0f}/QALY")
print(f"净货币效益: ¥{outcome.net_monetary_benefit:,.0f}")
```

### 模型校准

```python
# 执行校准
calibration_results = model.calibrate_model(
    use_neural_network=True,
    n_samples=10000
)

print(f"校准损失: {calibration_results['best_loss']:.6f}")
print("最佳参数:")
for param, value in calibration_results['best_parameters'].items():
    print(f"  {param}: {value:.6f}")
```

### 数据管理

```python
# 保存模型状态
model.save_model_state("my_simulation")

# 加载模型状态
model.load_model_state("my_simulation")

# 导出结果
model.export_results(format='excel', output_dir='results')

# 生成图表
model.plot_results(save_path='results/plots.png')
```

## API参考

### 核心类

#### ColorectalCancerMicrosimulationModel

主模型类，提供完整的模拟功能。

**构造函数**
```python
ColorectalCancerMicrosimulationModel(
    initial_population: int = 10000,
    config: Optional[ModelConfiguration] = None
)
```

**主要方法**

- `setup_population(age_distribution, gender_ratio)`: 初始化人口
- `run_simulation(years, screening_strategy, show_progress=True)`: 运行模拟
- `compare_strategies(strategy_names, years, show_progress=True)`: 比较策略
- `calibrate_model(use_neural_network=True, n_samples=10000)`: 校准模型
- `save_model_state(filename)`: 保存模型状态
- `load_model_state(filename)`: 加载模型状态
- `export_results(format='csv', output_dir='exports')`: 导出结果
- `plot_results(save_path=None)`: 绘制结果图表

#### Individual

个体类，表示模拟中的单个个体。

**属性**
- `id`: 个体ID
- `gender`: 性别 (Gender枚举)
- `birth_year`: 出生年份
- `current_age`: 当前年龄
- `alive`: 是否存活
- `cancer_stage`: 癌症阶段 (CancerStage枚举)
- `adenomas`: 腺瘤列表
- `screening_history`: 筛查历史
- `treatment_history`: 治疗历史

#### ScreeningStrategy

筛查策略类。

**构造函数**
```python
ScreeningStrategy(
    name: str,
    start_age: int,
    end_age: int,
    tools: List[ScreeningTool],
    intervals: List[int],
    sequential: bool = False,
    description: str = ""
)
```

### 枚举类型

#### Gender
- `MALE`: 男性
- `FEMALE`: 女性

#### CancerStage
- `NORMAL`: 正常状态
- `LOW_RISK_ADENOMA`: 低风险腺瘤
- `HIGH_RISK_ADENOMA`: 高风险腺瘤
- `SERRATED_ADENOMA`: 锯齿状腺瘤
- `PRECLINICAL_CANCER`: 临床前癌症
- `CLINICAL_CANCER_STAGE_I`: 临床癌症I期
- `CLINICAL_CANCER_STAGE_II`: 临床癌症II期
- `CLINICAL_CANCER_STAGE_III`: 临床癌症III期
- `CLINICAL_CANCER_STAGE_IV`: 临床癌症IV期

#### ScreeningTool
- `FIT`: 粪便免疫化学试验
- `COLONOSCOPY`: 结肠镜检查
- `SIGMOIDOSCOPY`: 乙状结肠镜检查
- `RISK_QUESTIONNAIRE`: 风险问卷

## 示例

### 示例1：基本模拟

```python
from ccsm.core.model import ColorectalCancerMicrosimulationModel

# 创建模型
model = ColorectalCancerMicrosimulationModel(initial_population=5000)

# 设置人口
age_distribution = {50: 0.4, 60: 0.6}
model.setup_population(age_distribution, 0.5)

# 运行模拟
results = model.run_simulation(10, "annual_fit")

# 查看结果
print(f"成本: ¥{results['economics_outcome']['total_cost']:,.0f}")
```

### 示例2：策略比较

```python
# 比较三种策略
strategies = ["annual_fit", "biennial_fit", "colonoscopy_10y"]
comparison = model.compare_strategies(strategies, 15)

# 查看最佳策略
if 'recommendations' in comparison:
    print("推荐策略:")
    for rec in comparison['recommendations']:
        print(f"- {rec}")
```

### 示例3：自定义参数

```python
from ccsm.modules.economics import EconomicParameters
from ccsm.core.enums import ScreeningTool

# 自定义经济学参数
econ_params = EconomicParameters(
    screening_costs={
        ScreeningTool.FIT: 80.0,  # 提高FIT成本
        ScreeningTool.COLONOSCOPY: 1500.0
    },
    willingness_to_pay_threshold=100000  # 降低支付意愿阈值
)

# 使用自定义参数创建模型
config = ModelConfiguration(economics_params=econ_params)
model = ColorectalCancerMicrosimulationModel(config=config)
```

### 示例4：敏感性分析

```python
from ccsm.modules.economics import EconomicsModule

# 创建经济学模块
economics = EconomicsModule()

# 定义参数变化范围
parameter_variations = {
    'discount_rate': [0.01, 0.03, 0.05, 0.07],
    'willingness_to_pay_threshold': [100000, 150000, 200000, 250000]
}

# 执行敏感性分析
sensitivity_results = economics.perform_sensitivity_analysis(
    "annual_fit",
    model.population_module.population,
    10,
    parameter_variations
)

# 查看结果
for param, values in sensitivity_results.items():
    print(f"{param}: {values}")
```

## 常见问题

### Q1: 如何设置随机种子以确保结果可重现？

```python
config = ModelConfiguration(random_seed=42)
model = ColorectalCancerMicrosimulationModel(config=config)
```

### Q2: 如何处理大规模人口模拟的内存问题？

- 使用较小的人口规模进行测试
- 分批处理大规模人口
- 定期保存中间结果
- 使用数据库存储而非内存

### Q3: 如何自定义疾病参数？

```python
from ccsm.modules.disease import DiseaseParameters

disease_params = DiseaseParameters(
    adenoma_generation_a=0.15,  # 修改腺瘤生成参数
    adenoma_generation_b=45.0,
    male_adenoma_multiplier=1.3  # 男性腺瘤风险倍数
)

config = ModelConfiguration(disease_params=disease_params)
model = ColorectalCancerMicrosimulationModel(config=config)
```

### Q4: 如何导出详细的个体级数据？

```python
# 导出人口数据
model.data_manager.file_manager.save_population_csv(
    model.population_module.population,
    "detailed_population"
)

# 导出筛查记录
screening_data = []
for individual in model.population_module.population:
    for record in individual.screening_history:
        screening_data.append({
            'individual_id': individual.id,
            'year': record.year,
            'tool': record.tool,
            'detected': record.detected,
            'cost': record.cost
        })

import pandas as pd
df = pd.DataFrame(screening_data)
df.to_csv('screening_records.csv', index=False)
```

### Q5: 如何添加新的筛查工具？

1. 在`enums.py`中添加新的`ScreeningTool`
2. 在`ScreeningParameters`中添加相应的敏感性和特异性参数
3. 在`ScreeningModule`中实现筛查逻辑
4. 在`EconomicParameters`中添加成本参数

## 故障排除

### 常见错误及解决方案

#### 1. ImportError: No module named 'ccsm'

**原因**: 包未正确安装
**解决方案**:
```bash
pip install -e .
# 或者
python setup.py develop
```

#### 2. ValueError: 模型未初始化

**原因**: 在运行模拟前未调用`setup_population()`
**解决方案**:
```python
model.setup_population(age_distribution, gender_ratio)
```

#### 3. MemoryError: 内存不足

**原因**: 人口规模过大
**解决方案**:
- 减少人口规模
- 增加系统内存
- 使用分批处理

#### 4. 校准收敛缓慢

**原因**: 参数范围设置不当或样本数不足
**解决方案**:
- 增加样本数
- 调整参数范围
- 使用更好的初始值

#### 5. 图表显示中文乱码

**原因**: matplotlib字体配置问题
**解决方案**:
```python
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
```

### 性能优化建议

1. **使用合适的人口规模**: 开发时使用小规模人口(1000-5000)，正式分析时使用大规模人口(50000+)

2. **并行处理**: 对于多策略比较，可以考虑并行执行

3. **数据库存储**: 对于大规模模拟，使用数据库而非内存存储

4. **定期保存**: 长时间模拟时定期保存中间结果

5. **参数调优**: 根据具体需求调整模拟参数

### 获取帮助

- **文档**: 查看完整的API文档
- **示例**: 参考examples目录中的示例代码
- **测试**: 运行测试用例了解预期行为
- **社区**: 在GitHub上提交issue或参与讨论

---

**版本**: 1.0.0
**最后更新**: 2024年7月
**作者**: CCSM开发团队