"""
卫生经济学评价模块
评估筛查策略的成本效益，包括成本计算、效益评估和经济学指标
"""

import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, field

from ..core.enums import ScreeningTool, CancerStage, Gender
from ..core.individual import Individual


@dataclass
class EconomicParameters:
    """经济学参数配置"""
    # 筛查成本（元）
    screening_costs: Dict[ScreeningTool, float] = field(default_factory=lambda: {
        ScreeningTool.FIT: 50.0,
        ScreeningTool.COLONOSCOPY: 1000.0,
        ScreeningTool.SIGMOIDOSCOPY: 300.0,
        ScreeningTool.RISK_QUESTIONNAIRE: 20.0
    })

    # 治疗成本（元）
    treatment_costs: Dict[str, float] = field(default_factory=lambda: {
        'adenoma_removal': 2000.0,
        'serrated_adenoma_removal': 2500.0,
        'preclinical_cancer_removal': 5000.0,
        'cancer_treatment_stage_1': 20000.0,
        'cancer_treatment_stage_2': 35000.0,
        'cancer_treatment_stage_3': 50000.0,
        'cancer_treatment_stage_4': 65000.0,
        'palliative_care': 30000.0
    })

    # 质量调整生命年（QALY）权重
    qaly_weights: Dict[str, float] = field(default_factory=lambda: {
        'healthy': 1.0,
        'low_risk_adenoma': 0.95,
        'high_risk_adenoma': 0.90,
        'preclinical_cancer': 0.85,
        'clinical_cancer_stage_1': 0.80,
        'clinical_cancer_stage_2': 0.70,
        'clinical_cancer_stage_3': 0.60,
        'clinical_cancer_stage_4': 0.40,
        'post_treatment': 0.85
    })

    # 年度折现率
    discount_rate: float = 0.03

    # 生命价值（元/年）
    value_of_life_year: float = 100000.0

    # 意愿支付阈值（元/QALY）
    willingness_to_pay_threshold: float = 200000.0


@dataclass
class EconomicOutcome:
    """经济学结果数据结构"""
    strategy_name: str
    total_screening_cost: float = 0.0
    total_treatment_cost: float = 0.0
    total_cost: float = 0.0
    life_years_gained: float = 0.0
    qalys_gained: float = 0.0
    cancer_cases_prevented: int = 0
    cancer_deaths_prevented: int = 0
    cost_per_life_year_saved: float = 0.0
    cost_per_qaly_gained: float = 0.0
    incremental_cost: float = 0.0
    incremental_effectiveness: float = 0.0
    incremental_cost_effectiveness_ratio: float = 0.0
    net_monetary_benefit: float = 0.0


class EconomicsModule:
    """卫生经济学评价模块"""

    def __init__(self, params: Optional[EconomicParameters] = None):
        self.params = params or EconomicParameters()
        self.baseline_outcome: Optional[EconomicOutcome] = None
        self.strategy_outcomes: Dict[str, EconomicOutcome] = {}

    def calculate_discounted_value(self, value: float, year: int, base_year: int = 0) -> float:
        """计算折现值"""
        years_diff = year - base_year
        if years_diff <= 0:
            return value
        return value / ((1 + self.params.discount_rate) ** years_diff)

    def calculate_qaly(self, individual: Individual, start_year: int, end_year: int) -> float:
        """计算个体的质量调整生命年"""
        total_qaly = 0.0

        for year in range(start_year, end_year + 1):
            if not individual.alive or (individual.death_year and year > individual.death_year):
                break

            # 根据疾病状态确定QALY权重
            if individual.cancer_stage == CancerStage.NORMAL:
                qaly_weight = self.params.qaly_weights['healthy']
            elif individual.cancer_stage == CancerStage.LOW_RISK_ADENOMA:
                qaly_weight = self.params.qaly_weights['low_risk_adenoma']
            elif individual.cancer_stage == CancerStage.HIGH_RISK_ADENOMA:
                qaly_weight = self.params.qaly_weights['high_risk_adenoma']
            elif individual.cancer_stage == CancerStage.PRECLINICAL_CANCER:
                qaly_weight = self.params.qaly_weights['preclinical_cancer']
            elif individual.cancer_stage == CancerStage.CLINICAL_CANCER_STAGE_I:
                qaly_weight = self.params.qaly_weights['clinical_cancer_stage_1']
            elif individual.cancer_stage == CancerStage.CLINICAL_CANCER_STAGE_II:
                qaly_weight = self.params.qaly_weights['clinical_cancer_stage_2']
            elif individual.cancer_stage == CancerStage.CLINICAL_CANCER_STAGE_III:
                qaly_weight = self.params.qaly_weights['clinical_cancer_stage_3']
            elif individual.cancer_stage == CancerStage.CLINICAL_CANCER_STAGE_IV:
                qaly_weight = self.params.qaly_weights['clinical_cancer_stage_4']
            else:
                qaly_weight = self.params.qaly_weights['healthy']

            # 如果接受过治疗，使用治疗后权重
            if individual.treatment_history:
                qaly_weight = max(qaly_weight, self.params.qaly_weights['post_treatment'])

            # 计算折现后的QALY
            discounted_qaly = self.calculate_discounted_value(qaly_weight, year, start_year)
            total_qaly += discounted_qaly

        return total_qaly

    def calculate_life_years(self, individual: Individual, start_year: int, end_year: int) -> float:
        """计算个体的生命年"""
        if individual.death_year:
            actual_end_year = min(individual.death_year, end_year)
        else:
            actual_end_year = end_year

        life_years = max(0, actual_end_year - start_year + 1)

        # 计算折现后的生命年
        discounted_life_years = 0.0
        for year in range(start_year, actual_end_year + 1):
            discounted_life_years += self.calculate_discounted_value(1.0, year, start_year)

        return discounted_life_years

    def evaluate_strategy(self, strategy_name: str, population: List[Individual],
                         simulation_years: int, start_year: int = 2020) -> EconomicOutcome:
        """评估筛查策略的经济学效果"""
        outcome = EconomicOutcome(strategy_name=strategy_name)

        total_screening_cost = 0.0
        total_treatment_cost = 0.0
        total_qalys = 0.0
        total_life_years = 0.0
        cancer_cases_prevented = 0
        cancer_deaths_prevented = 0

        for individual in population:
            # 计算筛查成本
            screening_cost = sum(record.cost for record in individual.screening_history)
            discounted_screening_cost = sum(
                self.calculate_discounted_value(record.cost, record.year, start_year)
                for record in individual.screening_history
            )
            total_screening_cost += discounted_screening_cost

            # 计算治疗成本
            treatment_cost = sum(record.cost for record in individual.treatment_history)
            discounted_treatment_cost = sum(
                self.calculate_discounted_value(record.cost, record.year, start_year)
                for record in individual.treatment_history
            )
            total_treatment_cost += discounted_treatment_cost

            # 计算QALY和生命年
            individual_qalys = self.calculate_qaly(individual, start_year, start_year + simulation_years)
            individual_life_years = self.calculate_life_years(individual, start_year, start_year + simulation_years)

            total_qalys += individual_qalys
            total_life_years += individual_life_years

            # 统计预防的癌症病例和死亡
            # 这里需要与无筛查基线进行比较，简化处理
            if individual.treatment_history:
                # 假设接受治疗的个体预防了癌症进展
                cancer_cases_prevented += 1

                # 根据治疗类型估算预防的死亡
                for treatment in individual.treatment_history:
                    if 'cancer_treatment' in treatment.treatment_type:
                        cancer_deaths_prevented += 1
                        break

        # 填充结果
        outcome.total_screening_cost = total_screening_cost
        outcome.total_treatment_cost = total_treatment_cost
        outcome.total_cost = total_screening_cost + total_treatment_cost
        outcome.life_years_gained = total_life_years
        outcome.qalys_gained = total_qalys
        outcome.cancer_cases_prevented = cancer_cases_prevented
        outcome.cancer_deaths_prevented = cancer_deaths_prevented

        # 计算成本效益比
        if outcome.life_years_gained > 0:
            outcome.cost_per_life_year_saved = outcome.total_cost / outcome.life_years_gained

        if outcome.qalys_gained > 0:
            outcome.cost_per_qaly_gained = outcome.total_cost / outcome.qalys_gained

        # 计算净货币效益
        outcome.net_monetary_benefit = (
            outcome.qalys_gained * self.params.willingness_to_pay_threshold - outcome.total_cost
        )

        # 保存结果
        self.strategy_outcomes[strategy_name] = outcome

        return outcome


    def set_baseline(self, strategy_name: str):
        """设置基线策略（通常是无筛查策略）"""
        if strategy_name in self.strategy_outcomes:
            self.baseline_outcome = self.strategy_outcomes[strategy_name]
        else:
            raise ValueError(f"策略 {strategy_name} 不存在，请先评估该策略")

    def calculate_incremental_analysis(self, strategy_name: str) -> EconomicOutcome:
        """计算增量成本效益分析"""
        if not self.baseline_outcome:
            raise ValueError("未设置基线策略，请先调用set_baseline()方法")

        if strategy_name not in self.strategy_outcomes:
            raise ValueError(f"策略 {strategy_name} 不存在，请先评估该策略")

        strategy_outcome = self.strategy_outcomes[strategy_name]

        # 计算增量指标
        strategy_outcome.incremental_cost = (
            strategy_outcome.total_cost - self.baseline_outcome.total_cost
        )
        strategy_outcome.incremental_effectiveness = (
            strategy_outcome.qalys_gained - self.baseline_outcome.qalys_gained
        )

        # 计算增量成本效益比（ICER）
        if strategy_outcome.incremental_effectiveness > 0:
            strategy_outcome.incremental_cost_effectiveness_ratio = (
                strategy_outcome.incremental_cost / strategy_outcome.incremental_effectiveness
            )
        elif strategy_outcome.incremental_effectiveness == 0:
            if strategy_outcome.incremental_cost > 0:
                strategy_outcome.incremental_cost_effectiveness_ratio = float('inf')
            else:
                strategy_outcome.incremental_cost_effectiveness_ratio = 0.0
        else:
            # 增量效益为负，策略被劣势支配
            strategy_outcome.incremental_cost_effectiveness_ratio = float('-inf')

        return strategy_outcome

    def compare_strategies(self, strategy_names: List[str]) -> Dict[str, Dict]:
        """比较多个策略的经济学效果"""
        if not all(name in self.strategy_outcomes for name in strategy_names):
            missing = [name for name in strategy_names if name not in self.strategy_outcomes]
            raise ValueError(f"以下策略尚未评估: {missing}")

        comparison_results = {}

        # 按总成本排序策略
        sorted_strategies = sorted(
            strategy_names,
            key=lambda x: self.strategy_outcomes[x].total_cost
        )

        # 进行成本效益分析
        for i, strategy_name in enumerate(sorted_strategies):
            outcome = self.strategy_outcomes[strategy_name]

            comparison_results[strategy_name] = {
                'rank_by_cost': i + 1,
                'total_cost': outcome.total_cost,
                'total_effectiveness': outcome.qalys_gained,
                'cost_per_qaly': outcome.cost_per_qaly_gained,
                'net_monetary_benefit': outcome.net_monetary_benefit,
                'dominated': False,
                'cost_effective': outcome.cost_per_qaly_gained <= self.params.willingness_to_pay_threshold
            }

            # 检查是否被劣势支配
            for other_name in strategy_names:
                if other_name == strategy_name:
                    continue

                other_outcome = self.strategy_outcomes[other_name]

                # 简单劣势支配：成本更高但效果更差
                if (outcome.total_cost >= other_outcome.total_cost and
                    outcome.qalys_gained <= other_outcome.qalys_gained and
                    (outcome.total_cost > other_outcome.total_cost or
                     outcome.qalys_gained < other_outcome.qalys_gained)):
                    comparison_results[strategy_name]['dominated'] = True
                    break

        return comparison_results

    def perform_sensitivity_analysis(self, strategy_name: str, parameter_variations: Dict[str, List[float]],
                                   population: List[Individual], simulation_years: int) -> Dict[str, List[float]]:
        """执行敏感性分析"""
        if strategy_name not in self.strategy_outcomes:
            raise ValueError(f"策略 {strategy_name} 不存在，请先评估该策略")

        sensitivity_results = {}

        for param_name, param_values in parameter_variations.items():
            results = []

            for param_value in param_values:
                # 创建参数副本
                temp_params = EconomicParameters(
                    screening_costs=self.params.screening_costs.copy(),
                    treatment_costs=self.params.treatment_costs.copy(),
                    qaly_weights=self.params.qaly_weights.copy(),
                    discount_rate=self.params.discount_rate,
                    value_of_life_year=self.params.value_of_life_year,
                    willingness_to_pay_threshold=self.params.willingness_to_pay_threshold
                )

                # 修改指定参数
                if param_name == 'discount_rate':
                    temp_params.discount_rate = param_value
                elif param_name == 'willingness_to_pay_threshold':
                    temp_params.willingness_to_pay_threshold = param_value
                elif param_name in temp_params.screening_costs:
                    temp_params.screening_costs[param_name] = param_value
                elif param_name in temp_params.treatment_costs:
                    temp_params.treatment_costs[param_name] = param_value

                # 创建临时经济学模块
                temp_economics = EconomicsModule(temp_params)
                temp_outcome = temp_economics.evaluate_strategy(
                    strategy_name, population, simulation_years
                )

                results.append(temp_outcome.cost_per_qaly_gained)

            sensitivity_results[param_name] = results

        return sensitivity_results

    def generate_cost_effectiveness_plane(self, strategy_names: List[str]) -> Dict[str, Tuple[float, float]]:
        """生成成本效益平面数据"""
        plane_data = {}

        if not self.baseline_outcome:
            # 如果没有基线，使用成本最低的策略作为参考
            baseline_strategy = min(
                strategy_names,
                key=lambda x: self.strategy_outcomes[x].total_cost
            )
            baseline_outcome = self.strategy_outcomes[baseline_strategy]
        else:
            baseline_outcome = self.baseline_outcome

        for strategy_name in strategy_names:
            if strategy_name not in self.strategy_outcomes:
                continue

            outcome = self.strategy_outcomes[strategy_name]

            # 计算相对于基线的增量成本和效益
            incremental_cost = outcome.total_cost - baseline_outcome.total_cost
            incremental_effectiveness = outcome.qalys_gained - baseline_outcome.qalys_gained

            plane_data[strategy_name] = (incremental_cost, incremental_effectiveness)

        return plane_data

    def export_economic_results(self, file_path: str, format: str = 'csv'):
        """导出经济学评价结果"""
        import pandas as pd

        if not self.strategy_outcomes:
            raise ValueError("没有可导出的结果，请先评估策略")

        # 准备导出数据
        data = []
        for strategy_name, outcome in self.strategy_outcomes.items():
            data.append({
                'strategy_name': outcome.strategy_name,
                'total_screening_cost': outcome.total_screening_cost,
                'total_treatment_cost': outcome.total_treatment_cost,
                'total_cost': outcome.total_cost,
                'life_years_gained': outcome.life_years_gained,
                'qalys_gained': outcome.qalys_gained,
                'cancer_cases_prevented': outcome.cancer_cases_prevented,
                'cancer_deaths_prevented': outcome.cancer_deaths_prevented,
                'cost_per_life_year_saved': outcome.cost_per_life_year_saved,
                'cost_per_qaly_gained': outcome.cost_per_qaly_gained,
                'incremental_cost': outcome.incremental_cost,
                'incremental_effectiveness': outcome.incremental_effectiveness,
                'incremental_cost_effectiveness_ratio': outcome.incremental_cost_effectiveness_ratio,
                'net_monetary_benefit': outcome.net_monetary_benefit
            })

        df = pd.DataFrame(data)

        if format.lower() == 'csv':
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
        elif format.lower() == 'excel':
            df.to_excel(file_path, index=False)
        elif format.lower() == 'json':
            df.to_json(file_path, orient='records', force_ascii=False, indent=2)
        else:
            raise ValueError(f"不支持的导出格式: {format}")

        print(f"经济学评价结果已导出到: {file_path}")

    def plot_cost_effectiveness_plane(self, strategy_names: List[str], save_path: Optional[str] = None):
        """绘制成本效益平面图"""
        try:
            import matplotlib.pyplot as plt
            import matplotlib
            matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文
            matplotlib.rcParams['axes.unicode_minus'] = False
        except ImportError:
            print("matplotlib未安装，无法绘制图表")
            return

        plane_data = self.generate_cost_effectiveness_plane(strategy_names)

        fig, ax = plt.subplots(figsize=(10, 8))

        # 绘制各策略点
        for strategy_name, (inc_cost, inc_eff) in plane_data.items():
            ax.scatter(inc_eff, inc_cost, s=100, alpha=0.7, label=strategy_name)
            ax.annotate(strategy_name, (inc_eff, inc_cost),
                       xytext=(5, 5), textcoords='offset points')

        # 绘制意愿支付阈值线
        max_eff = max(data[1] for data in plane_data.values()) if plane_data else 1
        threshold_line_x = np.linspace(0, max_eff * 1.1, 100)
        threshold_line_y = threshold_line_x * self.params.willingness_to_pay_threshold
        ax.plot(threshold_line_x, threshold_line_y, '--', color='red', alpha=0.7,
                label=f'意愿支付阈值 (¥{self.params.willingness_to_pay_threshold:,.0f}/QALY)')

        # 设置坐标轴
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax.axvline(x=0, color='black', linestyle='-', alpha=0.3)

        ax.set_xlabel('增量效益 (QALYs)')
        ax.set_ylabel('增量成本 (元)')
        ax.set_title('成本效益平面图')
        ax.legend()
        ax.grid(True, alpha=0.3)

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"成本效益平面图已保存到: {save_path}")
        else:
            plt.show()

        plt.close()

    def get_economic_summary(self) -> Dict[str, any]:
        """获取经济学评价摘要"""
        if not self.strategy_outcomes:
            return {}

        summary = {
            'total_strategies_evaluated': len(self.strategy_outcomes),
            'baseline_strategy': self.baseline_outcome.strategy_name if self.baseline_outcome else None,
            'most_cost_effective': None,
            'least_cost_effective': None,
            'highest_net_benefit': None,
            'strategies_summary': {}
        }

        # 找出最具成本效益的策略
        valid_strategies = [
            (name, outcome) for name, outcome in self.strategy_outcomes.items()
            if outcome.cost_per_qaly_gained <= self.params.willingness_to_pay_threshold
        ]

        if valid_strategies:
            most_cost_effective = min(valid_strategies, key=lambda x: x[1].cost_per_qaly_gained)
            summary['most_cost_effective'] = most_cost_effective[0]

        # 找出成本效益最差的策略
        if self.strategy_outcomes:
            least_cost_effective = max(
                self.strategy_outcomes.items(),
                key=lambda x: x[1].cost_per_qaly_gained if x[1].cost_per_qaly_gained != float('inf') else 0
            )
            summary['least_cost_effective'] = least_cost_effective[0]

        # 找出净效益最高的策略
        if self.strategy_outcomes:
            highest_net_benefit = max(
                self.strategy_outcomes.items(),
                key=lambda x: x[1].net_monetary_benefit
            )
            summary['highest_net_benefit'] = highest_net_benefit[0]

        # 策略摘要
        for name, outcome in self.strategy_outcomes.items():
            summary['strategies_summary'][name] = {
                'cost_per_qaly': outcome.cost_per_qaly_gained,
                'net_monetary_benefit': outcome.net_monetary_benefit,
                'cost_effective': outcome.cost_per_qaly_gained <= self.params.willingness_to_pay_threshold
            }

        return summary