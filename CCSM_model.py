import numpy as np
import pandas as pd
from scipy.stats import norm, logistic
import random
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.neural_network import <PERSON><PERSON><PERSON><PERSON>ressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error
import warnings
warnings.filterwarnings('ignore')

# 定义枚举类型
class Gender(Enum):
    MALE = 0
    FEMALE = 1

class CancerStage(Enum):
    NORMAL = 0
    LOW_RISK_ADENOMA = 1
    HIGH_RISK_ADENOMA = 2
    PRECLINICAL_CANCER = 3
    CLINICAL_CANCER_STAGE_I = 4
    CLINICAL_CANCER_STAGE_II = 5
    CLINICAL_CANCER_STAGE_III = 6
    CLINICAL_CANCER_STAGE_IV = 7

class ScreeningTool(Enum):
    FIT = "FIT"
    COLONOSCOPY = "Colonoscopy"
    SIGMOIDOSCOPY = "Sigmoidoscopy"
    RISK_QUESTIONNAIRE = "RiskQuestionnaire"
    OTHER = "Other"

# 个体数据类
@dataclass
class Individual:
    id: int
    gender: Gender
    birth_year: int
    current_age: int
    alive: bool = True
    cancer_stage: CancerStage = CancerStage.NORMAL
    adenoma_count: int = 0
    risk_score: float = 0.0
    last_screening_year: Optional[int] = None
    screening_history: List[Dict] = None
    death_cause: str = "alive"
    
    def __post_init__(self):
        if self.screening_history is None:
            self.screening_history = []

# 筛查工具参数
class ScreeningParameters:
    def __init__(self):
        # 各种筛查工具的敏感性和特异性
        self.sensitivity = {
            ScreeningTool.FIT: {
                CancerStage.LOW_RISK_ADENOMA: 0.70,
                CancerStage.HIGH_RISK_ADENOMA: 0.80,
                CancerStage.PRECLINICAL_CANCER: 0.90,
                CancerStage.CLINICAL_CANCER_STAGE_I: 0.95,
                CancerStage.CLINICAL_CANCER_STAGE_II: 0.95,
                CancerStage.CLINICAL_CANCER_STAGE_III: 0.95,
                CancerStage.CLINICAL_CANCER_STAGE_IV: 0.95
            },
            ScreeningTool.COLONOSCOPY: {
                CancerStage.LOW_RISK_ADENOMA: 0.95,
                CancerStage.HIGH_RISK_ADENOMA: 0.95,
                CancerStage.PRECLINICAL_CANCER: 0.95,
                CancerStage.CLINICAL_CANCER_STAGE_I: 0.95,
                CancerStage.CLINICAL_CANCER_STAGE_II: 0.95,
                CancerStage.CLINICAL_CANCER_STAGE_III: 0.95,
                CancerStage.CLINICAL_CANCER_STAGE_IV: 0.95
            },
            ScreeningTool.SIGMOIDOSCOPY: {
                CancerStage.LOW_RISK_ADENOMA: 0.70,
                CancerStage.HIGH_RISK_ADENOMA: 0.80,
                CancerStage.PRECLINICAL_CANCER: 0.85,
                CancerStage.CLINICAL_CANCER_STAGE_I: 0.90,
                CancerStage.CLINICAL_CANCER_STAGE_II: 0.90,
                CancerStage.CLINICAL_CANCER_STAGE_III: 0.90,
                CancerStage.CLINICAL_CANCER_STAGE_IV: 0.90
            }
        }
        
        self.specificity = {
            ScreeningTool.FIT: 0.95,
            ScreeningTool.COLONOSCOPY: 0.98,
            ScreeningTool.SIGMOIDOSCOPY: 0.96
        }
        
        # 依从性参数
        self.compliance = {
            ScreeningTool.FIT: 0.70,
            ScreeningTool.COLONOSCOPY: 0.60,
            ScreeningTool.SIGMOIDOSCOPY: 0.65,
            ScreeningTool.RISK_QUESTIONNAIRE: 0.80
        }

# 人口模块
class PopulationModule:
    def __init__(self, initial_population: int, start_year: int = 2020):
        self.initial_population = initial_population
        self.start_year = start_year
        self.population = []
        self.life_table = self._load_life_table()
        
    def _load_life_table(self):
        # 简化的中国生命表数据（实际应用中应使用官方统计数据）
        life_table = {}
        # 男性死亡率
        male_mortality = [0.001, 0.0005, 0.0002, 0.0001, 0.0001, 0.0001, 0.0001, 0.0002, 0.0003, 0.0005,
                         0.001, 0.002, 0.003, 0.004, 0.005, 0.007, 0.01, 0.015, 0.02, 0.025,
                         0.03, 0.035, 0.04, 0.045, 0.05, 0.055, 0.06, 0.065, 0.07, 0.075,
                         0.08, 0.085, 0.09, 0.095, 0.10, 0.11, 0.12, 0.13, 0.14, 0.15,
                         0.16, 0.17, 0.18, 0.19, 0.20, 0.21, 0.22, 0.23, 0.24, 0.25,
                         0.26, 0.27, 0.28, 0.29, 0.30, 0.31, 0.32, 0.33, 0.34, 0.35,
                         0.36, 0.37, 0.38, 0.39, 0.40, 0.41, 0.42, 0.43, 0.44, 0.45,
                         0.46, 0.47, 0.48, 0.49, 0.50, 0.51, 0.52, 0.53, 0.54, 0.55,
                         0.56, 0.57, 0.58, 0.59, 0.60, 0.61, 0.62, 0.63, 0.64, 0.65,
                         0.66, 0.67, 0.68, 0.69, 0.70, 0.71, 0.72, 0.73, 0.74, 0.75,
                         0.76, 0.77, 0.78, 0.79, 0.80, 0.81, 0.82, 0.83, 0.84, 0.85,
                         0.86, 0.87, 0.88, 0.89, 0.90, 0.91, 0.92, 0.93, 0.94, 0.95]
        
        # 女性死亡率
        female_mortality = [0.0008, 0.0004, 0.00015, 0.00008, 0.00008, 0.00008, 0.00008, 0.00015, 0.00025, 0.0004,
                           0.0008, 0.0015, 0.0025, 0.0035, 0.0045, 0.006, 0.008, 0.012, 0.016, 0.02,
                           0.024, 0.028, 0.032, 0.036, 0.04, 0.044, 0.048, 0.052, 0.056, 0.06,
                           0.064, 0.068, 0.072, 0.076, 0.08, 0.085, 0.09, 0.095, 0.10, 0.105,
                           0.11, 0.115, 0.12, 0.125, 0.13, 0.135, 0.14, 0.145, 0.15, 0.155,
                           0.16, 0.165, 0.17, 0.175, 0.18, 0.185, 0.19, 0.195, 0.20, 0.205,
                           0.21, 0.215, 0.22, 0.225, 0.23, 0.235, 0.24, 0.245, 0.25, 0.255,
                           0.26, 0.265, 0.27, 0.275, 0.28, 0.285, 0.29, 0.295, 0.30, 0.305,
                           0.31, 0.315, 0.32, 0.325, 0.33, 0.335, 0.34, 0.345, 0.35, 0.355,
                           0.36, 0.365, 0.37, 0.375, 0.38, 0.385, 0.39, 0.395, 0.40, 0.405,
                           0.41, 0.415, 0.42, 0.425, 0.43, 0.435, 0.44, 0.445, 0.45, 0.455,
                           0.46, 0.465, 0.47, 0.475, 0.48, 0.485, 0.49, 0.495, 0.50, 0.505]
        
        for age in range(100):
            life_table[(Gender.MALE, age)] = male_mortality[age] if age < len(male_mortality) else 1.0
            life_table[(Gender.FEMALE, age)] = female_mortality[age] if age < len(female_mortality) else 1.0
            
        return life_table
    
    def initialize_population(self, age_distribution: Dict[int, float], gender_ratio: float = 0.5) -> List[Individual]:
        """初始化人口队列"""
        population = []
        for i in range(self.initial_population):
            # 随机生成性别
            gender = Gender.MALE if random.random() < gender_ratio else Gender.FEMALE
            
            # 根据年龄分布随机生成年龄
            ages = list(age_distribution.keys())
            age_probs = list(age_distribution.values())
            age = np.random.choice(ages, p=age_probs)
            
            individual = Individual(
                id=i,
                gender=gender,
                birth_year=self.start_year - age,
                current_age=age
            )
            population.append(individual)
        
        self.population = population
        return population
    
    def update_population(self, current_year: int) -> List[Individual]:
        """更新人口状态（死亡等）"""
        alive_population = []
        for individual in self.population:
            if not individual.alive:
                continue
                
            age = current_year - individual.birth_year
            if age >= 100:
                individual.alive = False
                individual.death_cause = "natural"
                continue
                
            # 根据生命表计算死亡概率
            death_prob = self.life_table.get((individual.gender, age), 0.5)
            if random.random() < death_prob:
                individual.alive = False
                individual.death_cause = "natural"
            else:
                individual.current_age = age
                alive_population.append(individual)
        
        self.population = alive_population
        return alive_population

# 疾病风险模块
class DiseaseRiskModule:
    def __init__(self):
        # 风险因素权重（基于文献综述）
        self.risk_factors = {
            'family_history': 1.5,  # 一级亲属史
            'inflammatory_bowel_disease': 2.0,  # 炎性肠病史
            'overweight': 1.2,  # 超重肥胖
            'diabetes': 1.3,  # 糖尿病
            'smoking': 1.4,  # 吸烟
            'sedentary_lifestyle': 1.2  # 静坐生活方式
        }
    
    def calculate_risk_score(self, individual_risk_factors: Dict[str, bool]) -> float:
        """计算个体风险评分"""
        risk_score = 1.0
        for factor, present in individual_risk_factors.items():
            if present and factor in self.risk_factors:
                risk_score *= self.risk_factors[factor]
        return risk_score
    
    def get_adenoma_probability(self, base_prob: float, age: int, gender: Gender, risk_score: float) -> float:
        """基于年龄、性别和风险评分计算腺瘤发生概率"""
        # 年龄效应（乙状函数分布）
        age_effect = 1 / (1 + np.exp(-(age - 50) / 10))
        
        # 性别效应
        gender_effect = 1.2 if gender == Gender.MALE else 1.0
        
        # 综合概率
        prob = base_prob * age_effect * gender_effect * risk_score
        return min(prob, 1.0)  # 限制最大概率为1

# 疾病自然史模块
class DiseaseNaturalHistoryModule:
    def __init__(self):
        self.sojourn_time_mean = 3.0  # 腺瘤停留时间均值（年）
        self.sojourn_time_std = 0.5    # 腺瘤停留时间标准差
        
    def sigmoid_function(self, x: float, a: float = 0.1, b: float = 50, c: float = 10) -> float:
        """乙状函数"""
        return c / (1 + np.exp(-a * (x - b)))
    
    def normal_function(self, x: float, mean: float, std: float) -> float:
        """正态分布函数"""
        return norm.pdf(x, mean, std)
    
    def generate_adenoma_progression(self, age: int, gender: Gender) -> Dict:
        """生成腺瘤进展路径"""
        progression = {
            'low_risk_to_high_risk_prob': self.normal_function(age, 60, 15),
            'high_risk_to_preclinical_prob': self.normal_function(age, 65, 12),
            'preclinical_to_clinical_prob': 0.1,  # 固定概率
            'sojourn_time': max(0.1, np.random.normal(self.sojourn_time_mean, self.sojourn_time_std))
        }
        return progression
    
    def progress_disease(self, individual: Individual, current_year: int) -> Individual:
        """疾病进展模拟"""
        if not individual.alive:
            return individual
            
        age = individual.current_age
        
        # 如果是正常状态，可能发展腺瘤
        if individual.cancer_stage == CancerStage.NORMAL:
            # 这里需要结合风险模块的结果
            pass
        # 其他阶段的进展逻辑...
        
        return individual

# 筛查模块
class ScreeningModule:
    def __init__(self):
        self.parameters = ScreeningParameters()
        self.screening_strategies = {}
    
    def add_screening_strategy(self, name: str, strategy: Dict):
        """添加筛查策略"""
        self.screening_strategies[name] = strategy
    
    def perform_screening(self, individual: Individual, tool: ScreeningTool, 
                         current_year: int, current_age: int) -> Dict:
        """执行筛查"""
        result = {
            'tool': tool,
            'year': current_year,
            'age': current_age,
            'detected': False,
            'true_positive': False,
            'false_positive': False,
            'false_negative': False,
            'compliant': False
        }
        
        # 检查依从性
        if random.random() < self.parameters.compliance[tool]:
            result['compliant'] = True
            
            # 根据当前疾病状态和工具敏感性判断是否检测到
            sensitivity = self.parameters.sensitivity[tool].get(individual.cancer_stage, 0)
            
            if random.random() < sensitivity:
                result['detected'] = True
                result['true_positive'] = True
            else:
                result['false_negative'] = True
        else:
            # 假阳性
            if random.random() < (1 - self.parameters.specificity[tool]):
                result['detected'] = True
                result['false_positive'] = True
        
        individual.screening_history.append(result)
        individual.last_screening_year = current_year
        
        return result

# 卫生经济学模块
class HealthEconomicsModule:
    def __init__(self):
        self.costs = {
            ScreeningTool.FIT: 50,
            ScreeningTool.COLONOSCOPY: 1000,
            ScreeningTool.SIGMOIDOSCOPY: 300,
            ScreeningTool.RISK_QUESTIONNAIRE: 20
        }
        
        self.treatment_costs = {
            'stage_I': 20000,
            'stage_II': 35000,
            'stage_III': 50000,
            'stage_IV': 80000
        }
        
        self.discount_rate = 0.03  # 年度折现率
    
    def calculate_screening_cost(self, screening_history: List[Dict]) -> float:
        """计算筛查成本"""
        total_cost = 0
        for record in screening_history:
            if record['compliant']:
                tool = record['tool']
                total_cost += self.costs.get(tool, 0)
        return total_cost
    
    def calculate_qaly_gain(self, with_screening_life_years: float, 
                          without_screening_life_years: float) -> float:
        """计算质量调整生命年增益"""
        return with_screening_life_years - without_screening_life_years
    
    def discount_cost(self, cost: float, years: int) -> float:
        """成本折现"""
        return cost / ((1 + self.discount_rate) ** years)

# 机器学习校准模块
class CalibrationModule:
    def __init__(self):
        self.model = None
        self.training_data = None
    
    def latin_hypercube_sampling(self, n_samples: int, n_params: int) -> np.ndarray:
        """拉丁超立方抽样"""
        samples = np.zeros((n_samples, n_params))
        for i in range(n_params):
            samples[:, i] = np.random.permutation(n_samples)
        samples = (samples + np.random.random((n_samples, n_params))) / n_samples
        return samples
    
    def generate_training_data(self, n_samples: int = 10000) -> Tuple[np.ndarray, np.ndarray]:
        """生成训练数据"""
        # 输入参数：腺瘤产生概率、低风险进展概率、高风险进展概率的系数
        input_params = self.latin_hypercube_sampling(n_samples, 9)  # 3*3个系数
        
        # 输出参数：各种流行病学指标
        output_params = np.random.random((n_samples, 5))  # 简化示例
        
        return input_params, output_params
    
    def train_calibration_model(self, X: np.ndarray, y: np.ndarray):
        """训练校准模型"""
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        self.model = MLPRegressor(
            hidden_layer_sizes=(100, 50),
            max_iter=1000,
            random_state=42
        )
        
        self.model.fit(X_train, y_train)
        
        # 评估模型
        y_pred = self.model.predict(X_test)
        mse = mean_squared_error(y_test, y_pred)
        print(f"校准模型MSE: {mse}")
    
    def calibrate_parameters(self, target_values: np.ndarray) -> np.ndarray:
        """参数校准"""
        if self.model is None:
            raise ValueError("模型未训练，请先调用train_calibration_model方法")
        
        # 简单的逆向优化（实际应用中可能需要更复杂的优化算法）
        # 这里只是示例框架
        best_params = np.random.random(9)
        return best_params

# 主模型类
class ColorectalCancerMicrosimulationModel:
    def __init__(self, initial_population: int = 10000):
        self.population_module = PopulationModule(initial_population)
        self.risk_module = DiseaseRiskModule()
        self.natural_history_module = DiseaseNaturalHistoryModule()
        self.screening_module = ScreeningModule()
        self.economics_module = HealthEconomicsModule()
        self.calibration_module = CalibrationModule()
        
        # 模型状态
        self.current_year = 2020
        self.simulation_results = {}
    
    def setup_population(self, age_distribution: Dict[int, float], gender_ratio: float = 0.5):
        """设置初始人口"""
        self.population = self.population_module.initialize_population(age_distribution, gender_ratio)
        print(f"初始化人口: {len(self.population)} 人")
    
    def run_simulation(self, years: int = 20, screening_strategy: str = None):
        """运行模拟"""
        print(f"开始模拟 {years} 年...")
        
        results = {
            'population_size': [],
            'cancer_incidence': [],
            'cancer_mortality': [],
            'screening_participation': [],
            'costs': []
        }
        
        for year in range(years):
            current_year = self.current_year + year
            
            # 更新人口状态
            alive_population = self.population_module.update_population(current_year)
            
            # 疾病进展
            for individual in alive_population:
                individual = self.natural_history_module.progress_disease(individual, current_year)
            
            # 执行筛查（如果指定了策略）
            if screening_strategy and screening_strategy in self.screening_module.screening_strategies:
                self._perform_screening_for_population(alive_population, screening_strategy, current_year)
            
            # 记录结果
            results['population_size'].append(len(alive_population))
            results['cancer_incidence'].append(self._count_cancer_incidence(alive_population))
            results['cancer_mortality'].append(self._count_cancer_deaths(alive_population))
            results['screening_participation'].append(self._count_screening_participation(alive_population))
            
            print(f"年份 {current_year}: 人口 {len(alive_population)}, 癌症发病率 {results['cancer_incidence'][-1]}")
        
        self.simulation_results = results
        return results
    
    def _perform_screening_for_population(self, population: List[Individual], 
                                        strategy_name: str, current_year: int):
        """为人群执行筛查"""
        strategy = self.screening_module.screening_strategies[strategy_name]
        # 实现筛查逻辑
        pass
    
    def _count_cancer_incidence(self, population: List[Individual]) -> int:
        """统计癌症发病率"""
        return sum(1 for ind in population 
                  if ind.cancer_stage in [CancerStage.CLINICAL_CANCER_STAGE_I,
                                        CancerStage.CLINICAL_CANCER_STAGE_II,
                                        CancerStage.CLINICAL_CANCER_STAGE_III,
                                        CancerStage.CLINICAL_CANCER_STAGE_IV])
    
    def _count_cancer_deaths(self, population: List[Individual]) -> int:
        """统计癌症死亡数"""
        return sum(1 for ind in population if ind.death_cause == "cancer")
    
    def _count_screening_participation(self, population: List[Individual]) -> int:
        """统计筛查参与人数"""
        return sum(1 for ind in population if ind.last_screening_year is not None)
    
    def add_screening_strategy(self, name: str, strategy: Dict):
        """添加筛查策略"""
        self.screening_module.add_screening_strategy(name, strategy)
    
    def plot_results(self):
        """绘制结果图表"""
        if not self.simulation_results:
            print("没有模拟结果可绘制")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        years = range(len(self.simulation_results['population_size']))
        
        # 人口变化
        axes[0, 0].plot(years, self.simulation_results['population_size'])
        axes[0, 0].set_title('人口规模变化')
        axes[0, 0].set_xlabel('年份')
        axes[0, 0].set_ylabel('人口数')
        
        # 癌症发病率
        axes[0, 1].plot(years, self.simulation_results['cancer_incidence'])
        axes[0, 1].set_title('癌症发病率')
        axes[0, 1].set_xlabel('年份')
        axes[0, 1].set_ylabel('发病人数')
        
        # 癌症死亡率
        axes[1, 0].plot(years, self.simulation_results['cancer_mortality'])
        axes[1, 0].set_title('癌症死亡数')
        axes[1, 0].set_xlabel('年份')
        axes[1, 0].set_ylabel('死亡人数')
        
        # 筛查参与率
        axes[1, 1].plot(years, self.simulation_results['screening_participation'])
        axes[1, 1].set_title('筛查参与人数')
        axes[1, 1].set_xlabel('年份')
        axes[1, 1].set_ylabel('参与人数')
        
        plt.tight_layout()
        plt.show()

# 使用示例
def main():
    # 创建模型实例
    model = ColorectalCancerMicrosimulationModel(initial_population=1000)
    
    # 设置年龄分布（简化示例）
    age_dist = {50: 0.1, 55: 0.15, 60: 0.2, 65: 0.25, 70: 0.2, 75: 0.1}
    
    # 初始化人口
    model.setup_population(age_dist, gender_ratio=0.5)
    
    # 添加筛查策略
    fit_strategy = {
        'start_age': 50,
        'end_age': 75,
        'interval': 1,  # 每年一次
        'tools': [ScreeningTool.FIT]
    }
    model.add_screening_strategy("annual_fit", fit_strategy)
    
    # 运行模拟
    results = model.run_simulation(years=10, screening_strategy="annual_fit")
    
    # 绘制结果
    model.plot_results()
    
    print("模拟完成！")

if __name__ == "__main__":
    main()