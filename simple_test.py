#!/usr/bin/env python3
"""
简单测试脚本
"""

print("开始测试...")

try:
    import sys
    import os
    from pathlib import Path
    
    # 添加项目路径
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    print("✅ 基本导入成功")
    
    # 测试枚举导入
    from src.ccsm.core.enums import Gender, CancerStage
    print("✅ 枚举导入成功")
    
    # 测试个体导入
    from src.ccsm.core.individual import Individual
    print("✅ 个体类导入成功")
    
    # 创建个体
    individual = Individual(
        id=1,
        gender=Gender.MALE,
        birth_year=1970,
        current_age=50
    )
    print(f"✅ 个体创建成功: {individual.id}")
    
    print("🎉 基本测试通过！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
