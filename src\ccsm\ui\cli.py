"""
命令行界面 (CLI)
提供简单的命令行界面用于模型配置和运行
"""

import argparse
import sys
import json
from typing import Dict, List, Optional
from pathlib import Path

from ..core.model import ColorectalCancerMicrosimulationModel
from ..modules.population import PopulationModule, PopulationParameters
from ..modules.disease import DiseaseNaturalHistoryModule, DiseaseParameters
from ..modules.screening import ScreeningModule, ScreeningParameters, ScreeningStrategy
from ..modules.economics import EconomicsModule, EconomicParameters
from ..modules.calibration import CalibrationModule, CalibrationParameters
from ..data.data_manager import DataManager
from ..core.enums import Gender, ScreeningTool


def create_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="结直肠癌筛查微观模拟模型 (CCSM)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 运行基本模拟
  ccsm run --population 10000 --years 20 --strategy annual_fit

  # 比较多个策略
  ccsm compare --population 5000 --years 15 --strategies annual_fit biennial_fit colonoscopy_10y

  # 执行校准
  ccsm calibrate --samples 1000

  # 导出结果
  ccsm export --format csv --output results.csv
        """
    )

    subparsers = parser.add_subparsers(dest='command', help='可用命令')

    # run 命令
    run_parser = subparsers.add_parser('run', help='运行模拟')
    run_parser.add_argument('--population', type=int, default=10000, help='人口规模')
    run_parser.add_argument('--years', type=int, default=20, help='模拟年数')
    run_parser.add_argument('--strategy', type=str, default='annual_fit', help='筛查策略')
    run_parser.add_argument('--start-age', type=int, default=50, help='筛查开始年龄')
    run_parser.add_argument('--end-age', type=int, default=75, help='筛查结束年龄')
    run_parser.add_argument('--gender-ratio', type=float, default=0.5, help='男性比例')
    run_parser.add_argument('--output', type=str, help='输出文件路径')
    run_parser.add_argument('--verbose', action='store_true', help='详细输出')

    # compare 命令
    compare_parser = subparsers.add_parser('compare', help='比较多个策略')
    compare_parser.add_argument('--population', type=int, default=10000, help='人口规模')
    compare_parser.add_argument('--years', type=int, default=20, help='模拟年数')
    compare_parser.add_argument('--strategies', nargs='+', required=True, help='要比较的策略列表')
    compare_parser.add_argument('--output', type=str, help='输出文件路径')
    compare_parser.add_argument('--plot', action='store_true', help='生成图表')

    # calibrate 命令
    calibrate_parser = subparsers.add_parser('calibrate', help='执行模型校准')
    calibrate_parser.add_argument('--samples', type=int, default=10000, help='拉丁超立方样本数')
    calibrate_parser.add_argument('--method', choices=['neural_network', 'simple'],
                                default='neural_network', help='校准方法')
    calibrate_parser.add_argument('--output', type=str, help='校准结果输出路径')

    # export 命令
    export_parser = subparsers.add_parser('export', help='导出结果')
    export_parser.add_argument('--format', choices=['csv', 'excel', 'json'],
                              default='csv', help='导出格式')
    export_parser.add_argument('--output', type=str, required=True, help='输出文件路径')
    export_parser.add_argument('--type', choices=['population', 'screening', 'economics'],
                              default='population', help='导出数据类型')

    # config 命令
    config_parser = subparsers.add_parser('config', help='配置管理')
    config_parser.add_argument('--create', action='store_true', help='创建默认配置文件')
    config_parser.add_argument('--load', type=str, help='加载配置文件')
    config_parser.add_argument('--save', type=str, help='保存配置文件')

    return parser


class CLIInterface:
    """命令行界面主类"""

    def __init__(self):
        self.model = None
        self.data_manager = DataManager()
        self.current_results = {}

    def run_simulation(self, args) -> Dict:
        """运行单个策略模拟"""
        if args.verbose:
            print(f"初始化模拟 - 人口: {args.population}, 年数: {args.years}")

        # 创建模型
        self.model = ColorectalCancerMicrosimulationModel(
            initial_population=args.population
        )

        # 设置年龄分布（简化为均匀分布）
        age_distribution = {}
        age_range = list(range(args.start_age, args.end_age + 1, 5))
        for age in age_range:
            age_distribution[age] = 1.0 / len(age_range)

        # 初始化人口
        if args.verbose:
            print("初始化人口...")
        self.model.setup_population(age_distribution, args.gender_ratio)

        # 添加筛查策略
        if args.verbose:
            print(f"设置筛查策略: {args.strategy}")

        # 预定义策略
        predefined_strategies = {
            'annual_fit': {
                'name': 'annual_fit',
                'start_age': args.start_age,
                'end_age': args.end_age,
                'tools': [ScreeningTool.FIT],
                'intervals': [1],
                'sequential': False
            },
            'biennial_fit': {
                'name': 'biennial_fit',
                'start_age': args.start_age,
                'end_age': args.end_age,
                'tools': [ScreeningTool.FIT],
                'intervals': [2],
                'sequential': False
            },
            'colonoscopy_10y': {
                'name': 'colonoscopy_10y',
                'start_age': args.start_age,
                'end_age': args.end_age,
                'tools': [ScreeningTool.COLONOSCOPY],
                'intervals': [10],
                'sequential': False
            }
        }

        if args.strategy in predefined_strategies:
            strategy_config = predefined_strategies[args.strategy]
            strategy = ScreeningStrategy(**strategy_config)
            self.model.add_screening_strategy(strategy)
        else:
            print(f"警告: 未知策略 {args.strategy}，使用默认策略")
            strategy = ScreeningStrategy(**predefined_strategies['annual_fit'])
            self.model.add_screening_strategy(strategy)

        # 运行模拟
        if args.verbose:
            print("开始模拟...")

        results = self.model.run_simulation(
            years=args.years,
            screening_strategy=args.strategy
        )

        self.current_results = results

        # 输出结果摘要
        print("\n=== 模拟结果摘要 ===")
        print(f"策略: {args.strategy}")
        print(f"模拟人口: {args.population}")
        print(f"模拟年数: {args.years}")

        if 'population_stats' in results:
            stats = results['population_stats']
            print(f"存活人口: {stats.get('alive_population', 'N/A')}")
            print(f"癌症死亡: {stats.get('cancer_deaths', 'N/A')}")

        if 'screening_stats' in results:
            stats = results['screening_stats']
            print(f"筛查覆盖率: {stats.get('screening_coverage', 0):.2%}")
            print(f"检出病例: {stats.get('detected_cases', 'N/A')}")
            print(f"总筛查成本: ¥{stats.get('total_cost', 0):,.0f}")

        # 保存结果
        if args.output:
            self.data_manager.file_manager.save_results(
                results, Path(args.output).stem, 'json'
            )
            print(f"\n结果已保存到: {args.output}")

        return results


    def compare_strategies(self, args) -> Dict:
        """比较多个策略"""
        print(f"比较策略: {', '.join(args.strategies)}")
        print(f"人口规模: {args.population}, 模拟年数: {args.years}")

        comparison_results = {}

        for strategy_name in args.strategies:
            print(f"\n运行策略: {strategy_name}")

            # 为每个策略创建独立的模拟参数
            strategy_args = argparse.Namespace(
                population=args.population,
                years=args.years,
                strategy=strategy_name,
                start_age=50,
                end_age=75,
                gender_ratio=0.5,
                output=None,
                verbose=False
            )

            try:
                results = self.run_simulation(strategy_args)
                comparison_results[strategy_name] = results
            except Exception as e:
                print(f"策略 {strategy_name} 运行失败: {e}")
                continue

        # 输出比较结果
        print("\n=== 策略比较结果 ===")
        print(f"{'策略名称':<15} {'筛查覆盖率':<12} {'检出病例':<10} {'总成本':<15}")
        print("-" * 60)

        for strategy_name, results in comparison_results.items():
            screening_stats = results.get('screening_stats', {})
            coverage = screening_stats.get('screening_coverage', 0)
            detected = screening_stats.get('detected_cases', 0)
            cost = screening_stats.get('total_cost', 0)

            print(f"{strategy_name:<15} {coverage:<12.2%} {detected:<10} ¥{cost:<14,.0f}")

        # 保存比较结果
        if args.output:
            self.data_manager.file_manager.save_results(
                comparison_results, Path(args.output).stem, 'json'
            )
            print(f"\n比较结果已保存到: {args.output}")

        # 生成图表
        if args.plot:
            try:
                self._plot_comparison_results(comparison_results)
            except ImportError:
                print("matplotlib未安装，无法生成图表")

        return comparison_results

    def run_calibration(self, args) -> Dict:
        """执行模型校准"""
        print(f"开始模型校准 - 样本数: {args.samples}, 方法: {args.method}")

        # 创建校准模块
        calibration_params = CalibrationParameters(n_samples=args.samples)
        calibration_module = CalibrationModule(calibration_params)

        # 加载默认校准目标
        calibration_module.load_default_targets()

        # 执行校准
        use_nn = (args.method == 'neural_network')
        results = calibration_module.calibrate(use_neural_network=use_nn)

        # 输出校准结果
        print("\n=== 校准结果 ===")
        print(f"校准方法: {results['calibration_method']}")
        print(f"最佳损失: {results['best_loss']:.6f}")
        print(f"样本数量: {results['n_samples']}")

        if 'validation_results' in results:
            validation = results['validation_results']
            print(f"置信区间覆盖率: {validation['overall_coverage']:.2%}")

        print("\n最佳参数:")
        for param_name, param_value in results['best_parameters'].items():
            print(f"  {param_name}: {param_value:.6f}")

        # 保存校准结果
        if args.output:
            calibration_module.export_calibration_results(args.output, 'json')
        else:
            # 使用默认文件名
            timestamp = __import__('datetime').datetime.now().strftime('%Y%m%d_%H%M%S')
            default_output = f"calibration_results_{timestamp}.json"
            calibration_module.export_calibration_results(default_output, 'json')

        return results

    def export_data(self, args) -> None:
        """导出数据"""
        print(f"导出数据类型: {args.type}, 格式: {args.format}")

        if not self.current_results:
            print("没有可导出的结果，请先运行模拟")
            return

        try:
            if args.type == 'population':
                # 导出人口数据
                if self.model and hasattr(self.model, 'population_module'):
                    population = self.model.population_module.population
                    if args.format == 'csv':
                        self.data_manager.file_manager.save_population_csv(
                            population, Path(args.output).stem
                        )
                    else:
                        # 转换为字典格式
                        pop_data = [ind.to_dict() for ind in population]
                        self.data_manager.file_manager.save_results(
                            {'population': pop_data}, Path(args.output).stem, args.format
                        )

            elif args.type == 'screening':
                # 导出筛查结果
                screening_data = self.current_results.get('screening_stats', {})
                self.data_manager.file_manager.save_results(
                    screening_data, Path(args.output).stem, args.format
                )

            elif args.type == 'economics':
                # 导出经济学结果
                economics_data = self.current_results.get('economics_stats', {})
                self.data_manager.file_manager.save_results(
                    economics_data, Path(args.output).stem, args.format
                )

            print(f"数据已导出到: {args.output}")

        except Exception as e:
            print(f"导出失败: {e}")

    def manage_config(self, args) -> None:
        """配置管理"""
        if args.create:
            # 创建默认配置文件
            default_config = {
                'population': {
                    'initial_size': 10000,
                    'max_age': 100,
                    'start_year': 2020
                },
                'disease': {
                    'adenoma_generation_a': 0.1,
                    'adenoma_generation_b': 50.0,
                    'adenoma_generation_c': 0.01
                },
                'screening': {
                    'default_strategy': 'annual_fit',
                    'start_age': 50,
                    'end_age': 75
                },
                'economics': {
                    'discount_rate': 0.03,
                    'willingness_to_pay_threshold': 200000.0
                }
            }

            config_file = 'ccsm_config.json'
            self.data_manager.file_manager.save_parameters(default_config, 'default_config')
            print(f"默认配置文件已创建: {config_file}")

        elif args.load:
            # 加载配置文件
            try:
                config = self.data_manager.file_manager.load_parameters(
                    Path(args.load).stem
                )
                print(f"配置文件已加载: {args.load}")
                print("配置内容:")
                print(json.dumps(config, indent=2, ensure_ascii=False))
            except Exception as e:
                print(f"加载配置文件失败: {e}")

        elif args.save:
            # 保存当前配置
            if hasattr(self, 'current_config'):
                self.data_manager.file_manager.save_parameters(
                    self.current_config, Path(args.save).stem
                )
                print(f"配置已保存到: {args.save}")
            else:
                print("没有当前配置可保存")

    def _plot_comparison_results(self, comparison_results: Dict) -> None:
        """绘制比较结果图表"""
        import matplotlib.pyplot as plt
        import matplotlib
        matplotlib.rcParams['font.sans-serif'] = ['SimHei']
        matplotlib.rcParams['axes.unicode_minus'] = False

        strategies = list(comparison_results.keys())
        coverages = []
        costs = []
        detected_cases = []

        for strategy_name in strategies:
            results = comparison_results[strategy_name]
            screening_stats = results.get('screening_stats', {})

            coverages.append(screening_stats.get('screening_coverage', 0))
            costs.append(screening_stats.get('total_cost', 0))
            detected_cases.append(screening_stats.get('detected_cases', 0))

        # 创建子图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))

        # 筛查覆盖率
        ax1.bar(strategies, [c * 100 for c in coverages])
        ax1.set_title('筛查覆盖率 (%)')
        ax1.set_ylabel('覆盖率 (%)')
        plt.setp(ax1.get_xticklabels(), rotation=45)

        # 总成本
        ax2.bar(strategies, [c / 1000 for c in costs])
        ax2.set_title('总成本 (千元)')
        ax2.set_ylabel('成本 (千元)')
        plt.setp(ax2.get_xticklabels(), rotation=45)

        # 检出病例
        ax3.bar(strategies, detected_cases)
        ax3.set_title('检出病例数')
        ax3.set_ylabel('病例数')
        plt.setp(ax3.get_xticklabels(), rotation=45)

        # 成本效益散点图
        ax4.scatter(detected_cases, costs)
        for i, strategy in enumerate(strategies):
            ax4.annotate(strategy, (detected_cases[i], costs[i]))
        ax4.set_xlabel('检出病例数')
        ax4.set_ylabel('总成本 (元)')
        ax4.set_title('成本效益关系')

        plt.tight_layout()

        # 保存图表
        timestamp = __import__('datetime').datetime.now().strftime('%Y%m%d_%H%M%S')
        plot_file = f"strategy_comparison_{timestamp}.png"
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        print(f"比较图表已保存到: {plot_file}")

        plt.close()


def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    cli = CLIInterface()

    try:
        if args.command == 'run':
            cli.run_simulation(args)

        elif args.command == 'compare':
            cli.compare_strategies(args)

        elif args.command == 'calibrate':
            cli.run_calibration(args)

        elif args.command == 'export':
            cli.export_data(args)

        elif args.command == 'config':
            cli.manage_config(args)

        else:
            print(f"未知命令: {args.command}")
            parser.print_help()

    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)

    except Exception as e:
        print(f"执行失败: {e}")
        if hasattr(args, 'verbose') and args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()