# CCSM 部署指南

## 目录

1. [环境准备](#环境准备)
2. [本地部署](#本地部署)
3. [服务器部署](#服务器部署)
4. [Docker部署](#docker部署)
5. [云平台部署](#云平台部署)
6. [性能优化](#性能优化)
7. [监控和维护](#监控和维护)

## 环境准备

### 系统要求

**最低配置**
- CPU: 2核心
- 内存: 4GB RAM
- 存储: 10GB 可用空间
- Python: 3.8+

**推荐配置**
- CPU: 4核心或更多
- 内存: 8GB RAM或更多
- 存储: 50GB SSD
- Python: 3.9+

### 依赖软件

```bash
# 更新系统包管理器
sudo apt update  # Ubuntu/Debian
# 或
sudo yum update  # CentOS/RHEL

# 安装Python和pip
sudo apt install python3 python3-pip python3-venv
# 或
sudo yum install python3 python3-pip

# 安装Git
sudo apt install git
# 或
sudo yum install git

# 安装数据库（可选）
sudo apt install sqlite3  # SQLite
sudo apt install postgresql postgresql-contrib  # PostgreSQL
```

## 本地部署

### 1. 获取源码

```bash
# 克隆仓库
git clone https://github.com/your-org/ccsm.git
cd ccsm

# 或下载发布版本
wget https://github.com/your-org/ccsm/archive/v1.0.0.tar.gz
tar -xzf v1.0.0.tar.gz
cd ccsm-1.0.0
```

### 2. 创建虚拟环境

```bash
# 创建虚拟环境
python3 -m venv ccsm_env

# 激活虚拟环境
source ccsm_env/bin/activate  # Linux/macOS
# 或
ccsm_env\Scripts\activate  # Windows
```

### 3. 安装依赖

```bash
# 升级pip
pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt

# 开发模式安装
pip install -e .
```

### 4. 验证安装

```bash
# 运行测试
python -m pytest src/ccsm/tests/

# 运行示例
python examples/basic_simulation.py

# 使用命令行工具
ccsm --help
```

## 服务器部署

### 1. 服务器准备

```bash
# 创建专用用户
sudo useradd -m -s /bin/bash ccsm
sudo usermod -aG sudo ccsm

# 切换到ccsm用户
sudo su - ccsm

# 创建应用目录
mkdir -p /home/<USER>/apps/ccsm
cd /home/<USER>/apps/ccsm
```

### 2. 部署应用

```bash
# 克隆代码
git clone https://github.com/your-org/ccsm.git .

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
pip install -e .

# 创建配置文件
cp config/production.conf.example config/production.conf
# 编辑配置文件
nano config/production.conf
```

### 3. 配置系统服务

创建systemd服务文件：

```bash
sudo nano /etc/systemd/system/ccsm.service
```

```ini
[Unit]
Description=CCSM Colorectal Cancer Screening Microsimulation Model
After=network.target

[Service]
Type=simple
User=ccsm
Group=ccsm
WorkingDirectory=/home/<USER>/apps/ccsm
Environment=PATH=/home/<USER>/apps/ccsm/venv/bin
ExecStart=/home/<USER>/apps/ccsm/venv/bin/python -m ccsm.ui.web_server
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start ccsm

# 设置开机自启
sudo systemctl enable ccsm

# 查看服务状态
sudo systemctl status ccsm
```

### 4. 配置反向代理

使用Nginx作为反向代理：

```bash
# 安装Nginx
sudo apt install nginx

# 创建配置文件
sudo nano /etc/nginx/sites-available/ccsm
```

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static/ {
        alias /home/<USER>/apps/ccsm/static/;
        expires 30d;
    }
}
```

启用配置：

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/ccsm /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

## Docker部署

### 1. 创建Dockerfile

```dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 安装应用
RUN pip install -e .

# 创建非root用户
RUN useradd -m -u 1000 ccsm && chown -R ccsm:ccsm /app
USER ccsm

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "-m", "ccsm.ui.web_server"]
```

### 2. 构建镜像

```bash
# 构建镜像
docker build -t ccsm:latest .

# 查看镜像
docker images
```

### 3. 运行容器

```bash
# 运行容器
docker run -d \
  --name ccsm-app \
  -p 8000:8000 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/config:/app/config \
  ccsm:latest

# 查看容器状态
docker ps

# 查看日志
docker logs ccsm-app
```

### 4. Docker Compose部署

创建`docker-compose.yml`：

```yaml
version: '3.8'

services:
  ccsm:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./config:/app/config
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
      - CCSM_ENV=production
    restart: unless-stopped
    depends_on:
      - db

  db:
    image: postgres:13
    environment:
      POSTGRES_DB: ccsm
      POSTGRES_USER: ccsm
      POSTGRES_PASSWORD: your_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - ccsm
    restart: unless-stopped

volumes:
  postgres_data:
```

启动服务：

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f ccsm
```

---

**注意**: 请根据实际环境调整配置参数和路径。在生产环境中，确保使用强密码和适当的安全配置。