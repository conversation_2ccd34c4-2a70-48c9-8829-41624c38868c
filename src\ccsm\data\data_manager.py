"""
数据管理模块
负责数据导入导出、结果存储、数据验证等功能
"""

import os
import json
import pickle
import sqlite3
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import pandas as pd
import numpy as np
from pathlib import Path

from ..core.individual import Individual
from ..core.enums import Gender, CancerStage, ScreeningTool


class DataValidator:
    """数据验证器"""

    @staticmethod
    def validate_population_data(data: List[Dict]) -> List[str]:
        """验证人口数据"""
        errors = []
        required_fields = ['id', 'gender', 'birth_year', 'current_age']

        for i, record in enumerate(data):
            # 检查必需字段
            for field in required_fields:
                if field not in record:
                    errors.append(f"记录 {i}: 缺少必需字段 '{field}'")

            # 验证数据类型和范围
            if 'gender' in record:
                if record['gender'] not in [0, 1, 'male', 'female']:
                    errors.append(f"记录 {i}: 性别值无效 '{record['gender']}'")

            if 'current_age' in record:
                if not isinstance(record['current_age'], (int, float)) or record['current_age'] < 0 or record['current_age'] > 120:
                    errors.append(f"记录 {i}: 年龄值无效 '{record['current_age']}'")

            if 'birth_year' in record:
                current_year = datetime.now().year
                if not isinstance(record['birth_year'], int) or record['birth_year'] < 1900 or record['birth_year'] > current_year:
                    errors.append(f"记录 {i}: 出生年份无效 '{record['birth_year']}'")

        return errors

    @staticmethod
    def validate_screening_parameters(params: Dict) -> List[str]:
        """验证筛查参数"""
        errors = []

        # 检查敏感性参数
        if 'sensitivity' in params:
            for tool, stages in params['sensitivity'].items():
                for stage, value in stages.items():
                    if not 0 <= value <= 1:
                        errors.append(f"敏感性参数无效: {tool}-{stage} = {value}")

        # 检查特异性参数
        if 'specificity' in params:
            for tool, value in params['specificity'].items():
                if not 0 <= value <= 1:
                    errors.append(f"特异性参数无效: {tool} = {value}")

        # 检查依从性参数
        if 'compliance' in params:
            for tool, value in params['compliance'].items():
                if not 0 <= value <= 1:
                    errors.append(f"依从性参数无效: {tool} = {value}")

        return errors


class DatabaseManager:
    """数据库管理器"""

    def __init__(self, db_path: str = "ccsm_data.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 创建人口表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS population (
                id INTEGER PRIMARY KEY,
                gender INTEGER,
                birth_year INTEGER,
                current_age INTEGER,
                alive BOOLEAN,
                cancer_stage INTEGER,
                death_year INTEGER,
                death_cause TEXT,
                risk_score REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建筛查记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS screening_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                individual_id INTEGER,
                year INTEGER,
                age INTEGER,
                tool TEXT,
                compliant BOOLEAN,
                detected BOOLEAN,
                true_positive BOOLEAN,
                false_positive BOOLEAN,
                false_negative BOOLEAN,
                cost REAL,
                FOREIGN KEY (individual_id) REFERENCES population (id)
            )
        ''')

        # 创建治疗记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS treatment_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                individual_id INTEGER,
                year INTEGER,
                age INTEGER,
                treatment_type TEXT,
                cancer_stage INTEGER,
                cost REAL,
                success BOOLEAN,
                FOREIGN KEY (individual_id) REFERENCES population (id)
            )
        ''')

        # 创建模拟结果表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS simulation_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                simulation_name TEXT,
                strategy_name TEXT,
                simulation_date TIMESTAMP,
                parameters TEXT,
                results TEXT
            )
        ''')

        conn.commit()
        conn.close()

    def save_population(self, population: List[Individual], simulation_name: str = "default"):
        """保存人口数据到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 清除旧数据
        cursor.execute("DELETE FROM population WHERE 1=1")
        cursor.execute("DELETE FROM screening_records WHERE 1=1")
        cursor.execute("DELETE FROM treatment_records WHERE 1=1")

        # 插入人口数据
        for individual in population:
            cursor.execute('''
                INSERT INTO population
                (id, gender, birth_year, current_age, alive, cancer_stage,
                 death_year, death_cause, risk_score)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                individual.id,
                individual.gender.value,
                individual.birth_year,
                individual.current_age,
                individual.alive,
                individual.cancer_stage.value,
                individual.death_year,
                individual.death_cause.value,
                individual.risk_score
            ))

            # 插入筛查记录
            for record in individual.screening_history:
                cursor.execute('''
                    INSERT INTO screening_records
                    (individual_id, year, age, tool, compliant, detected,
                     true_positive, false_positive, false_negative, cost)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    individual.id,
                    record.year,
                    record.age,
                    record.tool,
                    record.compliant,
                    record.detected,
                    record.true_positive,
                    record.false_positive,
                    record.false_negative,
                    record.cost
                ))

            # 插入治疗记录
            for record in individual.treatment_history:
                cursor.execute('''
                    INSERT INTO treatment_records
                    (individual_id, year, age, treatment_type, cancer_stage, cost, success)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    individual.id,
                    record.year,
                    record.age,
                    record.treatment_type,
                    record.cancer_stage.value,
                    record.cost,
                    record.success
                ))

        conn.commit()
        conn.close()
        print(f"已保存 {len(population)} 个个体的数据到数据库")

    def load_population(self) -> List[Individual]:
        """从数据库加载人口数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 加载人口基本信息
        cursor.execute("SELECT * FROM population ORDER BY id")
        population_data = cursor.fetchall()

        population = []
        for row in population_data:
            individual = Individual(
                id=row[0],
                gender=Gender(row[1]),
                birth_year=row[2],
                current_age=row[3],
                alive=bool(row[4]),
                cancer_stage=CancerStage(row[5]),
                death_year=row[6],
                risk_score=row[8] if row[8] else 1.0
            )

            # 加载筛查记录
            cursor.execute(
                "SELECT * FROM screening_records WHERE individual_id = ? ORDER BY year",
                (individual.id,)
            )
            screening_data = cursor.fetchall()

            for s_row in screening_data:
                from ..core.individual import ScreeningRecord
                record = ScreeningRecord(
                    year=s_row[2],
                    age=s_row[3],
                    tool=s_row[4],
                    compliant=bool(s_row[5]),
                    detected=bool(s_row[6]),
                    true_positive=bool(s_row[7]),
                    false_positive=bool(s_row[8]),
                    false_negative=bool(s_row[9]),
                    cost=s_row[10]
                )
                individual.screening_history.append(record)

            # 加载治疗记录
            cursor.execute(
                "SELECT * FROM treatment_records WHERE individual_id = ? ORDER BY year",
                (individual.id,)
            )
            treatment_data = cursor.fetchall()

            for t_row in treatment_data:
                from ..core.individual import TreatmentRecord
                record = TreatmentRecord(
                    year=t_row[2],
                    age=t_row[3],
                    treatment_type=t_row[4],
                    cancer_stage=CancerStage(t_row[5]),
                    cost=t_row[6],
                    success=bool(t_row[7])
                )
                individual.treatment_history.append(record)

            population.append(individual)

        conn.close()
        print(f"从数据库加载了 {len(population)} 个个体的数据")
        return population


    def save_simulation_results(self, simulation_name: str, strategy_name: str,
                              parameters: Dict, results: Dict):
        """保存模拟结果"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO simulation_results
            (simulation_name, strategy_name, simulation_date, parameters, results)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            simulation_name,
            strategy_name,
            datetime.now().isoformat(),
            json.dumps(parameters, ensure_ascii=False),
            json.dumps(results, ensure_ascii=False, default=str)
        ))

        conn.commit()
        conn.close()
        print(f"已保存模拟结果: {simulation_name} - {strategy_name}")

    def load_simulation_results(self, simulation_name: Optional[str] = None) -> List[Dict]:
        """加载模拟结果"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        if simulation_name:
            cursor.execute(
                "SELECT * FROM simulation_results WHERE simulation_name = ? ORDER BY simulation_date DESC",
                (simulation_name,)
            )
        else:
            cursor.execute("SELECT * FROM simulation_results ORDER BY simulation_date DESC")

        results_data = cursor.fetchall()

        results = []
        for row in results_data:
            result = {
                'id': row[0],
                'simulation_name': row[1],
                'strategy_name': row[2],
                'simulation_date': row[3],
                'parameters': json.loads(row[4]),
                'results': json.loads(row[5])
            }
            results.append(result)

        conn.close()
        return results


class FileManager:
    """文件管理器"""

    def __init__(self, base_path: str = "data"):
        self.base_path = Path(base_path)
        self.base_path.mkdir(exist_ok=True)

        # 创建子目录
        (self.base_path / "population").mkdir(exist_ok=True)
        (self.base_path / "parameters").mkdir(exist_ok=True)
        (self.base_path / "results").mkdir(exist_ok=True)
        (self.base_path / "exports").mkdir(exist_ok=True)

    def save_population_csv(self, population: List[Individual], filename: str):
        """保存人口数据为CSV格式"""
        data = []
        for individual in population:
            row = individual.to_dict()

            # 添加筛查统计
            row['total_screenings'] = len(individual.screening_history)
            row['total_treatments'] = len(individual.treatment_history)
            row['total_screening_cost'] = individual.total_screening_cost
            row['total_treatment_cost'] = individual.total_treatment_cost

            data.append(row)

        df = pd.DataFrame(data)
        file_path = self.base_path / "population" / f"{filename}.csv"
        df.to_csv(file_path, index=False, encoding='utf-8-sig')
        print(f"人口数据已保存到: {file_path}")

    def load_population_csv(self, filename: str) -> List[Individual]:
        """从CSV文件加载人口数据"""
        file_path = self.base_path / "population" / f"{filename}.csv"

        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")

        df = pd.read_csv(file_path)

        # 验证数据
        data = df.to_dict('records')
        errors = DataValidator.validate_population_data(data)
        if errors:
            raise ValueError(f"数据验证失败:\n" + "\n".join(errors))

        population = []
        for _, row in df.iterrows():
            individual = Individual(
                id=int(row['id']),
                gender=Gender.MALE if row['gender'] == 0 else Gender.FEMALE,
                birth_year=int(row['birth_year']),
                current_age=int(row['current_age']),
                alive=bool(row['alive']),
                cancer_stage=CancerStage(int(row['cancer_stage'])),
                death_year=int(row['death_year']) if pd.notna(row['death_year']) else None,
                risk_score=float(row['risk_score']) if pd.notna(row['risk_score']) else 1.0
            )
            population.append(individual)

        print(f"从CSV文件加载了 {len(population)} 个个体的数据")
        return population

    def save_parameters(self, parameters: Dict, filename: str):
        """保存参数配置"""
        file_path = self.base_path / "parameters" / f"{filename}.json"

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(parameters, f, indent=2, ensure_ascii=False, default=str)

        print(f"参数配置已保存到: {file_path}")

    def load_parameters(self, filename: str) -> Dict:
        """加载参数配置"""
        file_path = self.base_path / "parameters" / f"{filename}.json"

        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")

        with open(file_path, 'r', encoding='utf-8') as f:
            parameters = json.load(f)

        print(f"已加载参数配置: {file_path}")
        return parameters

    def save_results(self, results: Dict, filename: str, format: str = 'json'):
        """保存结果数据"""
        if format.lower() == 'json':
            file_path = self.base_path / "results" / f"{filename}.json"
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)

        elif format.lower() == 'pickle':
            file_path = self.base_path / "results" / f"{filename}.pkl"
            with open(file_path, 'wb') as f:
                pickle.dump(results, f)

        elif format.lower() == 'csv':
            file_path = self.base_path / "results" / f"{filename}.csv"
            # 将结果转换为DataFrame
            if isinstance(results, dict) and 'data' in results:
                df = pd.DataFrame(results['data'])
            else:
                df = pd.DataFrame([results])
            df.to_csv(file_path, index=False, encoding='utf-8-sig')

        else:
            raise ValueError(f"不支持的格式: {format}")

        print(f"结果数据已保存到: {file_path}")

    def load_results(self, filename: str, format: str = 'json') -> Dict:
        """加载结果数据"""
        if format.lower() == 'json':
            file_path = self.base_path / "results" / f"{filename}.json"
            with open(file_path, 'r', encoding='utf-8') as f:
                results = json.load(f)

        elif format.lower() == 'pickle':
            file_path = self.base_path / "results" / f"{filename}.pkl"
            with open(file_path, 'rb') as f:
                results = pickle.load(f)

        else:
            raise ValueError(f"不支持的格式: {format}")

        print(f"已加载结果数据: {file_path}")
        return results

    def export_summary_report(self, simulation_results: Dict, filename: str):
        """导出汇总报告"""
        file_path = self.base_path / "exports" / f"{filename}.html"

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>CCSM模拟结果报告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .header {{ background-color: #4CAF50; color: white; padding: 10px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>结直肠癌筛查微观模拟模型 - 结果报告</h1>
                <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>

            <h2>模拟概要</h2>
            <table>
                <tr><th>项目</th><th>值</th></tr>
                <tr><td>模拟策略数</td><td>{len(simulation_results.get('strategies', {}))}</td></tr>
                <tr><td>模拟人口</td><td>{simulation_results.get('population_size', 'N/A')}</td></tr>
                <tr><td>模拟年数</td><td>{simulation_results.get('simulation_years', 'N/A')}</td></tr>
            </table>

            <h2>策略比较</h2>
            <table>
                <tr>
                    <th>策略名称</th>
                    <th>总成本</th>
                    <th>QALYs</th>
                    <th>成本/QALY</th>
                    <th>净效益</th>
                </tr>
        """

        for strategy_name, results in simulation_results.get('strategies', {}).items():
            html_content += f"""
                <tr>
                    <td>{strategy_name}</td>
                    <td>¥{results.get('total_cost', 0):,.0f}</td>
                    <td>{results.get('qalys_gained', 0):.2f}</td>
                    <td>¥{results.get('cost_per_qaly_gained', 0):,.0f}</td>
                    <td>¥{results.get('net_monetary_benefit', 0):,.0f}</td>
                </tr>
            """

        html_content += """
            </table>

            <h2>结论</h2>
            <p>基于模拟结果，推荐的筛查策略为最具成本效益的策略。</p>

        </body>
        </html>
        """

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"汇总报告已导出到: {file_path}")

    def list_files(self, directory: str = None) -> Dict[str, List[str]]:
        """列出文件"""
        if directory:
            target_path = self.base_path / directory
            if not target_path.exists():
                return {}

            files = [f.name for f in target_path.iterdir() if f.is_file()]
            return {directory: files}

        else:
            file_list = {}
            for subdir in ['population', 'parameters', 'results', 'exports']:
                subdir_path = self.base_path / subdir
                if subdir_path.exists():
                    files = [f.name for f in subdir_path.iterdir() if f.is_file()]
                    file_list[subdir] = files

            return file_list


class DataManager:
    """数据管理主类"""

    def __init__(self, db_path: str = "ccsm_data.db", file_base_path: str = "data"):
        self.db_manager = DatabaseManager(db_path)
        self.file_manager = FileManager(file_base_path)
        self.validator = DataValidator()

    def backup_data(self, backup_name: str):
        """备份数据"""
        import shutil

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = Path(f"backups/{backup_name}_{timestamp}")
        backup_dir.mkdir(parents=True, exist_ok=True)

        # 备份数据库
        if os.path.exists(self.db_manager.db_path):
            shutil.copy2(self.db_manager.db_path, backup_dir / "database.db")

        # 备份文件
        if self.file_manager.base_path.exists():
            shutil.copytree(self.file_manager.base_path, backup_dir / "files")

        print(f"数据已备份到: {backup_dir}")

    def restore_data(self, backup_path: str):
        """恢复数据"""
        import shutil

        backup_dir = Path(backup_path)
        if not backup_dir.exists():
            raise FileNotFoundError(f"备份目录不存在: {backup_path}")

        # 恢复数据库
        db_backup = backup_dir / "database.db"
        if db_backup.exists():
            shutil.copy2(db_backup, self.db_manager.db_path)

        # 恢复文件
        files_backup = backup_dir / "files"
        if files_backup.exists():
            if self.file_manager.base_path.exists():
                shutil.rmtree(self.file_manager.base_path)
            shutil.copytree(files_backup, self.file_manager.base_path)

        print(f"数据已从备份恢复: {backup_path}")

    def get_storage_info(self) -> Dict[str, Any]:
        """获取存储信息"""
        info = {
            'database_size': 0,
            'files_size': 0,
            'total_size': 0,
            'file_counts': {}
        }

        # 数据库大小
        if os.path.exists(self.db_manager.db_path):
            info['database_size'] = os.path.getsize(self.db_manager.db_path)

        # 文件大小
        if self.file_manager.base_path.exists():
            total_size = 0
            for root, dirs, files in os.walk(self.file_manager.base_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    total_size += os.path.getsize(file_path)
            info['files_size'] = total_size

        info['total_size'] = info['database_size'] + info['files_size']

        # 文件数量
        info['file_counts'] = self.file_manager.list_files()

        return info