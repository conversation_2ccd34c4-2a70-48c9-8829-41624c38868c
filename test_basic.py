"""
基本功能测试脚本
测试CCSM项目的核心功能是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试核心模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        # 测试核心模块
        from src.ccsm.core.enums import Gender, CancerStage, ScreeningTool
        print("✅ 核心枚举导入成功")
        
        from src.ccsm.core.individual import Individual, Adenoma
        print("✅ 个体数据结构导入成功")
        
        # 测试功能模块
        from src.ccsm.modules.population import PopulationModule, PopulationParameters
        print("✅ 人口模块导入成功")
        
        from src.ccsm.modules.disease import DiseaseNaturalHistoryModule, DiseaseParameters
        print("✅ 疾病模块导入成功")
        
        from src.ccsm.modules.screening import ScreeningModule, ScreeningStrategy
        print("✅ 筛查模块导入成功")
        
        from src.ccsm.modules.economics import EconomicsModule, EconomicParameters
        print("✅ 经济学模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_individual_creation():
    """测试个体创建"""
    print("\n=== 测试个体创建 ===")
    
    try:
        from src.ccsm.core.individual import Individual
        from src.ccsm.core.enums import Gender, CancerStage
        
        # 创建个体
        individual = Individual(
            id=1,
            gender=Gender.MALE,
            birth_year=1970,
            current_age=50
        )
        
        print(f"✅ 个体创建成功: ID={individual.id}, 性别={individual.gender.name}, 年龄={individual.current_age}")
        
        # 测试基本属性
        assert individual.alive == True
        assert individual.cancer_stage == CancerStage.NORMAL
        print("✅ 个体属性验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 个体创建失败: {e}")
        return False

def test_population_module():
    """测试人口模块"""
    print("\n=== 测试人口模块 ===")
    
    try:
        from src.ccsm.modules.population import PopulationModule, PopulationParameters
        
        # 创建人口模块
        params = PopulationParameters()
        pop_module = PopulationModule(initial_population=100, params=params)
        
        # 初始化人口
        age_distribution = {50: 0.5, 60: 0.5}
        population = pop_module.initialize_population(age_distribution, 0.5)
        
        print(f"✅ 人口初始化成功: 总人数={len(population)}")
        
        # 验证人口属性
        ages = [ind.current_age for ind in population]
        assert all(age in [50, 60] for age in ages)
        print("✅ 年龄分布验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 人口模块测试失败: {e}")
        return False

def test_screening_strategy():
    """测试筛查策略"""
    print("\n=== 测试筛查策略 ===")
    
    try:
        from src.ccsm.modules.screening import ScreeningStrategy
        from src.ccsm.core.enums import ScreeningTool
        
        # 创建筛查策略
        strategy = ScreeningStrategy(
            name="test_strategy",
            start_age=50,
            end_age=75,
            tools=[ScreeningTool.FIT],
            intervals=[1]
        )
        
        print(f"✅ 筛查策略创建成功: {strategy.name}")
        
        # 验证策略属性
        assert strategy.start_age == 50
        assert strategy.end_age == 75
        assert len(strategy.tools) == 1
        print("✅ 筛查策略属性验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 筛查策略测试失败: {e}")
        return False

def test_economics_module():
    """测试经济学模块"""
    print("\n=== 测试经济学模块 ===")
    
    try:
        from src.ccsm.modules.economics import EconomicsModule, EconomicParameters
        
        # 创建经济学模块
        params = EconomicParameters()
        econ_module = EconomicsModule(params)
        
        print(f"✅ 经济学模块创建成功")
        
        # 验证参数
        assert params.discount_rate == 0.03
        assert params.willingness_to_pay_threshold == 200000.0
        print("✅ 经济学参数验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 经济学模块测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始CCSM项目基本功能测试...\n")
    
    tests = [
        test_imports,
        test_individual_creation,
        test_population_module,
        test_screening_strategy,
        test_economics_module
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有基本功能测试通过！")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关模块")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
