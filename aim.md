请用python语言帮我开发软件，具体要求如下
1.构建国内首个基于多源异构数据融合的结直肠癌筛查微观模拟模型，突破现有模型本土化不足的技术瓶颈，为优化我国结直肠癌筛查策略提供科学工具。具体目标包括：
1.2.1模型功能：
支持自然人群队列和出生队列动态模拟的双重架构；
集成个体疾病风险模块；
兼容粪便免疫化学检测（FIT）、结肠镜、乙状结直肠镜、风险评估问卷等主流筛查工具及其筛查策略；
可预测筛查策略对结直肠癌发病、死亡的长期影响效果；
内嵌筛查策略卫生经济学评价模块。
1.2.2技术突破​
首创支持非固定筛查周期、多筛查工具组合贯序实施的模拟引擎；
实现基于机器学习的自适应校准功能，建立疾病自然史动态调参机制。

2.模型模块：
人口模块：包括人口数、性别、年龄、存活状态等参数。模拟的人口为动态衰减人口，不考虑人口迁徙及人口出生，随着队列中个体去世，队列规模逐渐变少。其中每个个体的性别根据性别比设定，年龄根据人群年龄构成比设定，运行到年龄最大到100岁或死亡；自然死亡率由人口寿命表作为数源。
疾病风险模块：检索PubMed、中国生物文献库、Embase、Web of Science等主流文献平台近十年结直肠癌疾病风险因素文献，汇总确定结直肠癌疾病风险及其对结直肠癌发病的风险值，优先纳入meta结果、大型队列研究结果，并在条件允许的情况下进行meta分析汇总。初步拟模拟风险因素包括结直肠癌一级亲属、炎性肠病史、超重肥胖、糖尿病、吸烟、静坐生活方式等，最终形成综合风险指数。本项目假设疾病风险只影响腺瘤的产生、不影响腺瘤和临床前癌症的进展。
疾病自然史模块：包括腺瘤-癌通路和锯齿状腺瘤-癌变通路。
疾病自然史腺瘤-癌通路中，分为腺瘤产生、进展、结局：
腺瘤产生：此处关联疾病风险模块，每个个体腺瘤的产生由年龄、性别、个体风险决定；根据肠镜/乙状肠镜达到和检出效果差异，腺瘤可分布结直肠不同部位，个体可产生多个腺瘤。
腺瘤进展：由年龄、性别决定，根据李志芳等专家对国内外相关文献的研究推荐（李志芳, 等. 中华流行病学杂志 2017,38(02): 253-260），腺瘤进展的阶段包括低风险腺瘤（≤9mm，且无绒毛无高级别上皮内瘤变）、高风险腺瘤（≥10mm或含绒毛或高级别上皮内瘤变），临床前癌症（无症状）和临床癌症（分别为I 至 IV 期）；
癌症结局：临床癌症期个体生存时长取决于诊断时年龄、癌症位置和癌症分期。结直肠导致的死亡仅限于诊断后5年内。
参数包括性别年龄别腺瘤产生概率、低风险腺瘤进展概率（低风险到高风险期腺瘤）、高风险腺瘤进展概率（高风险期到临床前期），腺瘤和癌症部位（分类变量），性别年龄别癌症进展概率，腺瘤停留时长（sojourn time，腺瘤产生到临床前癌症时长）、临床前癌症停留时长(dwell time，临床前癌症到癌症时长)，不同期癌症生存时长等。其中年龄别腺瘤产生概率为乙状函数分布，年龄别低风险期腺瘤进展概率为正态函数分布、年龄别高风险期腺瘤进展概率为正态函数分布；本项目设定性别影响腺瘤产生、腺瘤进展的效果为常数；腺瘤停留时长为正态分布（均值3年、标准差0.5年）；临床前癌症停留时长由调参决定。
锯齿状病变—癌症通路：包括正常，小无蒂锯齿状腺瘤、大无蒂锯齿状腺瘤，临床前癌（无症状）和临床癌症（分别为I 至 IV 期）。
筛查模块：筛查可以发现腺瘤和临床前癌症，通过手术摘除腺瘤或临床前癌症，从而中止疾病进程。模型支持的筛查工具包括肠镜、乙状肠镜、FIT、风险评估问卷、其他等。除肠镜检查外，每种工具都分别具有针对不同期腺瘤、癌症的灵敏度、特异度、筛查依从性以及阳性后接收肠镜检查的依从性。
筛查策略包括筛查起止年龄、筛查间隔，筛查工具是否贯序实施，筛查实施周期等。支持筛查贯序实施，将与我国现行绝大部分试点筛查策略高度匹配（一般先进行风险问卷调查，然后进行FIT检测、最后进行肠镜确诊）。本项目假设多轮筛查过程中依从性恒定，后期可以考虑动态依从性（如依从性依次递减5%）。
卫生经济学评价模块：经济成本包括各类筛查成本、临床治疗直接成本，所有成本均取3%的年度折现率；效益包括挽救生命年（通过比较筛查与不筛查个体生存时长得到）。
3.模型校准
自动校准：CMOST在校准国内基准参数过程中，很容易出现数据过早溢出、需要人为去修改的腺瘤患病率和癌症发病率数据分布函数的系数，数据校准纯粹是碰运气。
本项目将采用机器学习方法——深度神经网络，优化模型校准过程，可以显著减少计算负担，实现模型的高效自动校准。模型入参为年龄别腺瘤产生概率（3个系数）、年龄别低风险期腺瘤进展概率（3个系数），年龄别高风险期腺瘤进展概率（3个系数），出参为性别年龄别低风险期腺瘤患病率、性别年龄别高风险期腺瘤患病率、性别年龄别癌症发病率、性别年龄别癌症死亡率、性别年龄别分布在直肠的癌症占比。本项目将采用拉丁超立方抽样法，生成10000个入参的可能范围和出参数值的组合，从而利用深度神经网络方法快速校准。最终模型模拟出参值的95%CI置信区间，需要涵盖出参的基准值。