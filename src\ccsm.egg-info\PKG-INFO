Metadata-Version: 2.4
Name: ccsm
Version: 1.0.0
Summary: 结直肠癌筛查微观模拟模型
Home-page: https://github.com/example/ccsm
Author: CCSM Development Team
Author-email: <EMAIL>
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Topic :: Scientific/Engineering :: Medical Science Apps.
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: numpy>=1.21.0
Requires-Dist: scipy>=1.7.0
Requires-Dist: pandas>=1.3.0
Requires-Dist: scikit-learn>=1.0.0
Requires-Dist: matplotlib>=3.5.0
Requires-Dist: tqdm>=4.60.0
Requires-Dist: pytest>=7.0.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-cov>=4.0.0; extra == "dev"
Requires-Dist: black>=22.0.0; extra == "dev"
Requires-Dist: flake8>=4.0.0; extra == "dev"
Requires-Dist: mypy>=0.950; extra == "dev"
Provides-Extra: docs
Requires-Dist: sphinx>=4.0.0; extra == "docs"
Requires-Dist: sphinx-rtd-theme>=1.0.0; extra == "docs"
Provides-Extra: web
Requires-Dist: flask>=2.0.0; extra == "web"
Requires-Dist: fastapi>=0.75.0; extra == "web"
Requires-Dist: uvicorn>=0.17.0; extra == "web"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

# 结直肠癌筛查微观模拟模型 (CCSM)

## 项目简介

结直肠癌筛查微观模拟模型（Colorectal Cancer Screening Microsimulation Model, CCSM）是国内首个基于多源异构数据融合的结直肠癌筛查微观模拟模型，旨在突破现有模型本土化不足的技术瓶颈，为优化我国结直肠癌筛查策略提供科学工具。

## 主要特性

- **双重架构支持**：支持自然人群队列和出生队列动态模拟
- **个体疾病风险模块**：集成多种风险因素的综合评估
- **多筛查工具兼容**：支持FIT、结肠镜、乙状结直肠镜、风险评估问卷等
- **长期影响预测**：可预测筛查策略对结直肠癌发病、死亡的长期影响
- **卫生经济学评价**：内嵌成本效益分析模块
- **机器学习校准**：基于深度神经网络的自适应校准功能

## 技术突破

1. **非固定筛查周期**：首创支持非固定筛查周期、多筛查工具组合贯序实施的模拟引擎
2. **自适应校准**：实现基于机器学习的自适应校准功能，建立疾病自然史动态调参机制

## 项目结构

```
CCSM/
├── src/ccsm/                 # 源代码
│   ├── core/                 # 核心模块
│   │   ├── enums.py         # 枚举类型定义
│   │   ├── individual.py    # 个体数据结构
│   │   └── model.py         # 主模型类
│   ├── modules/             # 功能模块
│   │   ├── population.py    # 人口模块
│   │   ├── disease.py       # 疾病模块
│   │   ├── screening.py     # 筛查模块
│   │   ├── economics.py     # 经济学模块
│   │   └── calibration.py   # 校准模块
│   ├── utils/               # 工具函数
│   ├── data/                # 数据文件
│   ├── config/              # 配置文件
│   ├── tests/               # 测试文件
│   └── ui/                  # 用户界面
├── docs/                    # 文档
├── examples/                # 示例代码
├── scripts/                 # 脚本文件
├── requirements.txt         # 依赖包
├── setup.py                # 安装配置
├── PRD.md                  # 产品需求文档
└── README.md               # 项目说明
```

## 安装指南

### 环境要求

- Python 3.8+
- 推荐使用虚拟环境

### 安装步骤

1. 克隆项目
```bash
git clone https://github.com/example/ccsm.git
cd ccsm
```

2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

4. 安装项目
```bash
pip install -e .
```

## 快速开始

### 基本使用

```python
from ccsm import ColorectalCancerMicrosimulationModel, Gender

# 创建模型实例
model = ColorectalCancerMicrosimulationModel(initial_population=10000)

# 设置年龄分布
age_distribution = {50: 0.1, 55: 0.15, 60: 0.2, 65: 0.25, 70: 0.2, 75: 0.1}

# 初始化人口
model.setup_population(age_distribution, gender_ratio=0.5)

# 添加筛查策略
strategy = {
    'name': 'annual_fit',
    'start_age': 50,
    'end_age': 75,
    'interval': 1,
    'tools': ['FIT']
}
model.add_screening_strategy(strategy)

# 运行模拟
results = model.run_simulation(years=20, screening_strategy='annual_fit')

# 查看结果
model.plot_results()
```

### 命令行使用

```bash
# 运行基本模拟
ccsm run --population 10000 --years 20 --strategy annual_fit

# 查看帮助
ccsm --help
```

## 模块说明

### 人口模块
- 模拟动态衰减人口
- 基于生命表计算自然死亡率
- 支持年龄分布和性别比例设定

### 疾病风险模块
- 集成多种风险因素
- 计算个体综合风险评分
- 支持风险因素权重自定义

### 疾病自然史模块
- 腺瘤-癌通路模拟
- 锯齿状腺瘤-癌变通路
- 疾病进展概率建模

### 筛查模块
- 多种筛查工具支持
- 筛查策略配置
- 贯序实施功能

### 卫生经济学模块
- 筛查成本计算
- 治疗成本评估
- QALY分析

### 机器学习校准模块
- 拉丁超立方抽样
- 深度神经网络训练
- 自动参数校准

## 配置说明

模型支持通过配置文件进行参数设置，配置文件位于 `src/ccsm/config/` 目录下。

## 测试

运行测试套件：

```bash
pytest src/ccsm/tests/
```

运行覆盖率测试：

```bash
pytest --cov=ccsm src/ccsm/tests/
```

## 文档

详细文档请参考：
- [用户手册](docs/user_manual.md)
- [API文档](docs/api_reference.md)
- [开发指南](docs/development_guide.md)

## 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 联系方式

- 项目主页：https://github.com/example/ccsm
- 问题反馈：https://github.com/example/ccsm/issues
- 邮箱：<EMAIL>

## 致谢

感谢所有为本项目做出贡献的研究人员和开发者。
