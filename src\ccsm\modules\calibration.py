"""
机器学习校准模块
基于深度神经网络的自动校准功能，实现疾病自然史动态调参机制
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
import random
from scipy.stats import qmc
import warnings

try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers
    TF_AVAILABLE = True
except ImportError:
    TF_AVAILABLE = False
    warnings.warn("TensorFlow未安装，将使用简化的校准方法")

try:
    from sklearn.model_selection import train_test_split
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import mean_squared_error, r2_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    warnings.warn("Scikit-learn未安装，某些功能可能受限")


@dataclass
class CalibrationTarget:
    """校准目标数据结构"""
    name: str                           # 目标名称
    age_groups: List[Tuple[int, int]]   # 年龄组
    gender: str                         # 性别 ('male', 'female', 'both')
    target_values: List[float]          # 目标值
    confidence_intervals: List[Tuple[float, float]]  # 95%置信区间
    weight: float = 1.0                 # 权重


@dataclass
class CalibrationParameters:
    """校准参数配置"""
    # 输入参数范围（用于拉丁超立方抽样）
    parameter_ranges: Dict[str, Tuple[float, float]] = field(default_factory=lambda: {
        # 腺瘤产生概率参数（乙状函数）
        'adenoma_generation_a': (0.05, 0.2),
        'adenoma_generation_b': (45.0, 55.0),
        'adenoma_generation_c': (0.005, 0.02),

        # 腺瘤进展概率参数（正态分布）
        'low_to_high_risk_mean': (55.0, 65.0),
        'low_to_high_risk_std': (10.0, 20.0),
        'high_to_preclinical_mean': (60.0, 70.0),
        'high_to_preclinical_std': (8.0, 16.0),

        # 性别效应参数
        'male_adenoma_multiplier': (1.1, 1.3),
        'male_progression_multiplier': (1.05, 1.15)
    })

    # 神经网络参数
    hidden_layers: List[int] = field(default_factory=lambda: [128, 64, 32])
    learning_rate: float = 0.001
    batch_size: int = 32
    epochs: int = 100
    validation_split: float = 0.2

    # 校准参数
    n_samples: int = 10000              # 拉丁超立方抽样数量
    max_iterations: int = 10            # 最大迭代次数
    convergence_threshold: float = 0.01  # 收敛阈值
    confidence_level: float = 0.95      # 置信水平


class LatinHypercubeSampler:
    """拉丁超立方抽样器"""

    def __init__(self, parameter_ranges: Dict[str, Tuple[float, float]]):
        self.parameter_ranges = parameter_ranges
        self.parameter_names = list(parameter_ranges.keys())
        self.n_parameters = len(self.parameter_names)

    def sample(self, n_samples: int, seed: Optional[int] = None) -> np.ndarray:
        """生成拉丁超立方样本"""
        if seed is not None:
            np.random.seed(seed)

        # 使用scipy的拉丁超立方抽样
        sampler = qmc.LatinHypercube(d=self.n_parameters, seed=seed)
        samples = sampler.random(n=n_samples)

        # 将[0,1]区间的样本映射到参数范围
        scaled_samples = np.zeros_like(samples)
        for i, param_name in enumerate(self.parameter_names):
            min_val, max_val = self.parameter_ranges[param_name]
            scaled_samples[:, i] = samples[:, i] * (max_val - min_val) + min_val

        return scaled_samples

    def samples_to_dict(self, samples: np.ndarray) -> List[Dict[str, float]]:
        """将样本数组转换为参数字典列表"""
        param_dicts = []
        for sample in samples:
            param_dict = {}
            for i, param_name in enumerate(self.parameter_names):
                param_dict[param_name] = sample[i]
            param_dicts.append(param_dict)
        return param_dicts


class ModelSimulator:
    """模型模拟器（用于校准）"""

    def __init__(self):
        # 这里应该导入实际的疾病模型
        # 为了演示，我们使用简化的模拟函数
        pass

    def simulate_with_parameters(self, parameters: Dict[str, float],
                               population_size: int = 1000,
                               simulation_years: int = 20) -> Dict[str, List[float]]:
        """
        使用给定参数运行模拟

        Args:
            parameters: 模型参数字典
            population_size: 人口规模
            simulation_years: 模拟年数

        Returns:
            模拟结果字典
        """
        # 简化的模拟逻辑（实际应该调用完整的疾病模型）
        results = {
            'male_low_risk_adenoma_prevalence': [],
            'male_high_risk_adenoma_prevalence': [],
            'male_cancer_incidence': [],
            'male_cancer_mortality': [],
            'male_rectal_cancer_proportion': [],
            'female_low_risk_adenoma_prevalence': [],
            'female_high_risk_adenoma_prevalence': [],
            'female_cancer_incidence': [],
            'female_cancer_mortality': [],
            'female_rectal_cancer_proportion': []
        }

        # 年龄组：50-54, 55-59, 60-64, 65-69, 70-74
        age_groups = [(50, 54), (55, 59), (60, 64), (65, 69), (70, 74)]

        for age_start, age_end in age_groups:
            age_mid = (age_start + age_end) / 2

            # 使用参数计算各种患病率和发病率
            # 这里是简化的计算，实际应该使用完整的疾病模型

            # 腺瘤产生概率（乙状函数）
            adenoma_prob = parameters['adenoma_generation_c'] / (
                1 + np.exp(-parameters['adenoma_generation_a'] *
                          (age_mid - parameters['adenoma_generation_b']))
            )

            # 男性
            male_adenoma_prob = adenoma_prob * parameters['male_adenoma_multiplier']
            male_low_risk_prev = male_adenoma_prob * 0.7  # 假设70%为低风险
            male_high_risk_prev = male_adenoma_prob * 0.3  # 假设30%为高风险
            male_cancer_inc = male_high_risk_prev * 0.1    # 假设10%进展为癌症
            male_cancer_mort = male_cancer_inc * 0.3       # 假设30%死亡率
            male_rectal_prop = 0.2                         # 假设20%为直肠癌

            results['male_low_risk_adenoma_prevalence'].append(male_low_risk_prev)
            results['male_high_risk_adenoma_prevalence'].append(male_high_risk_prev)
            results['male_cancer_incidence'].append(male_cancer_inc)
            results['male_cancer_mortality'].append(male_cancer_mort)
            results['male_rectal_cancer_proportion'].append(male_rectal_prop)

            # 女性
            female_adenoma_prob = adenoma_prob  # 女性无倍数效应
            female_low_risk_prev = female_adenoma_prob * 0.7
            female_high_risk_prev = female_adenoma_prob * 0.3
            female_cancer_inc = female_high_risk_prev * 0.08  # 女性进展率稍低
            female_cancer_mort = female_cancer_inc * 0.25     # 女性死亡率稍低
            female_rectal_prop = 0.15                         # 女性直肠癌比例稍低

            results['female_low_risk_adenoma_prevalence'].append(female_low_risk_prev)
            results['female_high_risk_adenoma_prevalence'].append(female_high_risk_prev)
            results['female_cancer_incidence'].append(female_cancer_inc)
            results['female_cancer_mortality'].append(female_cancer_mort)
            results['female_rectal_cancer_proportion'].append(female_rectal_prop)

        return results


class DeepNeuralNetworkCalibrator:
    """深度神经网络校准器"""

    def __init__(self, params: CalibrationParameters):
        self.params = params
        self.model = None
        self.scaler_X = None
        self.scaler_y = None
        self.training_history = None

        if not TF_AVAILABLE:
            raise ImportError("TensorFlow未安装，无法使用深度神经网络校准")

    def _build_model(self, input_dim: int, output_dim: int) -> keras.Model:
        """构建深度神经网络模型"""
        model = keras.Sequential()

        # 输入层
        model.add(layers.Dense(self.params.hidden_layers[0],
                              activation='relu',
                              input_shape=(input_dim,)))
        model.add(layers.Dropout(0.2))

        # 隐藏层
        for hidden_size in self.params.hidden_layers[1:]:
            model.add(layers.Dense(hidden_size, activation='relu'))
            model.add(layers.Dropout(0.2))

        # 输出层
        model.add(layers.Dense(output_dim, activation='linear'))

        # 编译模型
        optimizer = keras.optimizers.Adam(learning_rate=self.params.learning_rate)
        model.compile(optimizer=optimizer,
                     loss='mse',
                     metrics=['mae'])

        return model

    def train(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """训练神经网络"""
        # 数据标准化
        if SKLEARN_AVAILABLE:
            self.scaler_X = StandardScaler()
            self.scaler_y = StandardScaler()
            X_scaled = self.scaler_X.fit_transform(X)
            y_scaled = self.scaler_y.fit_transform(y)
        else:
            # 简单标准化
            X_scaled = (X - np.mean(X, axis=0)) / np.std(X, axis=0)
            y_scaled = (y - np.mean(y, axis=0)) / np.std(y, axis=0)

        # 构建模型
        self.model = self._build_model(X.shape[1], y.shape[1])

        # 训练模型
        callbacks = [
            keras.callbacks.EarlyStopping(patience=10, restore_best_weights=True),
            keras.callbacks.ReduceLROnPlateau(factor=0.5, patience=5)
        ]

        history = self.model.fit(
            X_scaled, y_scaled,
            batch_size=self.params.batch_size,
            epochs=self.params.epochs,
            validation_split=self.params.validation_split,
            callbacks=callbacks,
            verbose=0
        )

        self.training_history = history.history

        # 评估模型
        train_loss = history.history['loss'][-1]
        val_loss = history.history['val_loss'][-1]

        return {
            'train_loss': train_loss,
            'val_loss': val_loss,
            'epochs_trained': len(history.history['loss']),
            'best_epoch': np.argmin(history.history['val_loss']) + 1
        }

    def predict(self, X: np.ndarray) -> np.ndarray:
        """使用训练好的模型进行预测"""
        if self.model is None:
            raise ValueError("模型尚未训练")

        # 标准化输入
        if self.scaler_X is not None:
            X_scaled = self.scaler_X.transform(X)
        else:
            X_scaled = X

        # 预测
        y_pred_scaled = self.model.predict(X_scaled, verbose=0)

        # 反标准化输出
        if self.scaler_y is not None:
            y_pred = self.scaler_y.inverse_transform(y_pred_scaled)
        else:
            y_pred = y_pred_scaled

        return y_pred

    def save_model(self, file_path: str):
        """保存模型"""
        if self.model is not None:
            self.model.save(file_path)

    def load_model(self, file_path: str):
        """加载模型"""
        self.model = keras.models.load_model(file_path)


class CalibrationModule:
    """校准模块主类"""

    def __init__(self, params: Optional[CalibrationParameters] = None):
        self.params = params or CalibrationParameters()
        self.sampler = LatinHypercubeSampler(self.params.parameter_ranges)
        self.simulator = ModelSimulator()
        self.calibrator = None
        self.calibration_targets = []
        self.calibration_results = {}

        if TF_AVAILABLE:
            self.calibrator = DeepNeuralNetworkCalibrator(self.params)

    def add_calibration_target(self, target: CalibrationTarget):
        """添加校准目标"""
        self.calibration_targets.append(target)

    def load_default_targets(self):
        """加载默认校准目标（基于中国人群数据）"""
        age_groups = [(50, 54), (55, 59), (60, 64), (65, 69), (70, 74)]

        # 男性低风险腺瘤患病率
        male_low_risk_target = CalibrationTarget(
            name="male_low_risk_adenoma_prevalence",
            age_groups=age_groups,
            gender="male",
            target_values=[0.15, 0.20, 0.25, 0.30, 0.35],  # 示例数据
            confidence_intervals=[(0.12, 0.18), (0.17, 0.23), (0.22, 0.28), (0.27, 0.33), (0.32, 0.38)],
            weight=1.0
        )
        self.add_calibration_target(male_low_risk_target)

        # 男性高风险腺瘤患病率
        male_high_risk_target = CalibrationTarget(
            name="male_high_risk_adenoma_prevalence",
            age_groups=age_groups,
            gender="male",
            target_values=[0.08, 0.12, 0.16, 0.20, 0.24],
            confidence_intervals=[(0.06, 0.10), (0.10, 0.14), (0.14, 0.18), (0.18, 0.22), (0.22, 0.26)],
            weight=1.0
        )
        self.add_calibration_target(male_high_risk_target)

        # 男性癌症发病率
        male_cancer_incidence_target = CalibrationTarget(
            name="male_cancer_incidence",
            age_groups=age_groups,
            gender="male",
            target_values=[0.001, 0.002, 0.004, 0.008, 0.015],
            confidence_intervals=[(0.0008, 0.0012), (0.0015, 0.0025), (0.003, 0.005), (0.006, 0.010), (0.012, 0.018)],
            weight=2.0  # 癌症发病率权重更高
        )
        self.add_calibration_target(male_cancer_incidence_target)

        # 女性目标（类似结构，数值稍低）
        female_low_risk_target = CalibrationTarget(
            name="female_low_risk_adenoma_prevalence",
            age_groups=age_groups,
            gender="female",
            target_values=[0.12, 0.16, 0.20, 0.24, 0.28],
            confidence_intervals=[(0.10, 0.14), (0.14, 0.18), (0.18, 0.22), (0.22, 0.26), (0.26, 0.30)],
            weight=1.0
        )
        self.add_calibration_target(female_low_risk_target)

        female_high_risk_target = CalibrationTarget(
            name="female_high_risk_adenoma_prevalence",
            age_groups=age_groups,
            gender="female",
            target_values=[0.06, 0.09, 0.12, 0.15, 0.18],
            confidence_intervals=[(0.05, 0.07), (0.08, 0.10), (0.11, 0.13), (0.14, 0.16), (0.17, 0.19)],
            weight=1.0
        )
        self.add_calibration_target(female_high_risk_target)

        female_cancer_incidence_target = CalibrationTarget(
            name="female_cancer_incidence",
            age_groups=age_groups,
            gender="female",
            target_values=[0.0008, 0.0015, 0.003, 0.006, 0.012],
            confidence_intervals=[(0.0006, 0.001), (0.0012, 0.0018), (0.0025, 0.0035), (0.005, 0.007), (0.010, 0.014)],
            weight=2.0
        )
        self.add_calibration_target(female_cancer_incidence_target)

    def _prepare_training_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """准备训练数据"""
        print("生成拉丁超立方样本...")
        samples = self.sampler.sample(self.params.n_samples, seed=42)
        param_dicts = self.sampler.samples_to_dict(samples)

        print("运行模拟获取训练数据...")
        X = []  # 输入参数
        y = []  # 输出结果

        for i, params in enumerate(param_dicts):
            if i % 1000 == 0:
                print(f"进度: {i}/{len(param_dicts)}")

            # 运行模拟
            simulation_results = self.simulator.simulate_with_parameters(params)

            # 构建输入向量
            input_vector = [params[name] for name in self.sampler.parameter_names]
            X.append(input_vector)

            # 构建输出向量（按目标顺序）
            output_vector = []
            for target in self.calibration_targets:
                if target.name in simulation_results:
                    output_vector.extend(simulation_results[target.name])
                else:
                    # 如果没有对应的模拟结果，用零填充
                    output_vector.extend([0.0] * len(target.target_values))
            y.append(output_vector)

        return np.array(X), np.array(y)

    def _calculate_loss(self, predicted: np.ndarray, targets: List[CalibrationTarget]) -> float:
        """计算校准损失"""
        total_loss = 0.0
        output_index = 0

        for target in targets:
            n_values = len(target.target_values)
            pred_values = predicted[output_index:output_index + n_values]
            target_values = np.array(target.target_values)

            # 计算加权均方误差
            mse = np.mean((pred_values - target_values) ** 2)
            weighted_loss = mse * target.weight
            total_loss += weighted_loss

            output_index += n_values

        return total_loss

    def _check_confidence_intervals(self, predicted: np.ndarray,
                                  targets: List[CalibrationTarget]) -> Dict[str, bool]:
        """检查预测值是否在置信区间内"""
        results = {}
        output_index = 0

        for target in targets:
            n_values = len(target.target_values)
            pred_values = predicted[output_index:output_index + n_values]

            # 检查每个年龄组的预测值是否在置信区间内
            in_ci = []
            for i, (pred_val, (ci_low, ci_high)) in enumerate(
                zip(pred_values, target.confidence_intervals)
            ):
                in_ci.append(ci_low <= pred_val <= ci_high)

            # 如果95%以上的预测值在置信区间内，认为通过
            pass_rate = sum(in_ci) / len(in_ci)
            results[target.name] = pass_rate >= 0.95

            output_index += n_values

        return results

    def calibrate(self, use_neural_network: bool = True) -> Dict[str, Any]:
        """执行校准"""
        if not self.calibration_targets:
            raise ValueError("未设置校准目标，请先调用load_default_targets()或add_calibration_target()")

        print("开始校准过程...")

        # 准备训练数据
        X, y = self._prepare_training_data()

        if use_neural_network and self.calibrator is not None:
            # 使用深度神经网络校准
            print("训练深度神经网络...")
            training_results = self.calibrator.train(X, y)

            # 寻找最佳参数
            print("搜索最佳参数...")
            best_params, best_loss = self._find_best_parameters_nn()

            calibration_method = "deep_neural_network"

        else:
            # 使用简单的最近邻搜索
            print("使用简单搜索方法...")
            best_params, best_loss = self._find_best_parameters_simple(X, y)
            training_results = {"method": "simple_search"}
            calibration_method = "simple_search"

        # 验证最佳参数
        print("验证最佳参数...")
        validation_results = self._validate_parameters(best_params)

        # 保存结果
        self.calibration_results = {
            'best_parameters': best_params,
            'best_loss': best_loss,
            'training_results': training_results,
            'validation_results': validation_results,
            'calibration_method': calibration_method,
            'n_samples': self.params.n_samples,
            'targets': [target.name for target in self.calibration_targets]
        }

        print(f"校准完成！最佳损失: {best_loss:.6f}")
        return self.calibration_results

    def _find_best_parameters_nn(self) -> Tuple[Dict[str, float], float]:
        """使用神经网络寻找最佳参数"""
        # 生成新的测试样本
        test_samples = self.sampler.sample(1000, seed=123)

        # 使用神经网络预测
        predictions = self.calibrator.predict(test_samples)

        # 计算每个样本的损失
        best_loss = float('inf')
        best_params = None

        for i, sample in enumerate(test_samples):
            pred = predictions[i]
            loss = self._calculate_loss(pred, self.calibration_targets)

            if loss < best_loss:
                best_loss = loss
                param_dict = {}
                for j, param_name in enumerate(self.sampler.parameter_names):
                    param_dict[param_name] = sample[j]
                best_params = param_dict

        return best_params, best_loss

    def _find_best_parameters_simple(self, X: np.ndarray, y: np.ndarray) -> Tuple[Dict[str, float], float]:
        """使用简单方法寻找最佳参数"""
        best_loss = float('inf')
        best_params = None

        # 构建目标向量
        target_vector = []
        for target in self.calibration_targets:
            target_vector.extend(target.target_values)
        target_vector = np.array(target_vector)

        # 寻找最接近目标的样本
        for i, (sample, output) in enumerate(zip(X, y)):
            loss = self._calculate_loss(output, self.calibration_targets)

            if loss < best_loss:
                best_loss = loss
                param_dict = {}
                for j, param_name in enumerate(self.sampler.parameter_names):
                    param_dict[param_name] = sample[j]
                best_params = param_dict

        return best_params, best_loss

    def _validate_parameters(self, parameters: Dict[str, float]) -> Dict[str, Any]:
        """验证参数"""
        # 使用最佳参数运行模拟
        simulation_results = self.simulator.simulate_with_parameters(parameters)

        # 构建预测向量
        predicted_vector = []
        for target in self.calibration_targets:
            if target.name in simulation_results:
                predicted_vector.extend(simulation_results[target.name])
            else:
                predicted_vector.extend([0.0] * len(target.target_values))

        predicted_vector = np.array(predicted_vector)

        # 检查置信区间
        ci_results = self._check_confidence_intervals(predicted_vector, self.calibration_targets)

        # 计算各项指标
        validation_results = {
            'confidence_interval_coverage': ci_results,
            'overall_coverage': sum(ci_results.values()) / len(ci_results),
            'simulation_results': simulation_results,
            'predicted_vs_target': {}
        }

        # 详细比较预测值与目标值
        output_index = 0
        for target in self.calibration_targets:
            n_values = len(target.target_values)
            pred_values = predicted_vector[output_index:output_index + n_values]

            validation_results['predicted_vs_target'][target.name] = {
                'predicted': pred_values.tolist(),
                'target': target.target_values,
                'relative_error': ((pred_values - np.array(target.target_values)) /
                                 np.array(target.target_values) * 100).tolist()
            }

            output_index += n_values

        return validation_results

    def get_calibrated_parameters(self) -> Optional[Dict[str, float]]:
        """获取校准后的参数"""
        if 'best_parameters' in self.calibration_results:
            return self.calibration_results['best_parameters']
        return None

    def export_calibration_results(self, file_path: str, format: str = 'json'):
        """导出校准结果"""
        if not self.calibration_results:
            raise ValueError("尚未执行校准，请先调用calibrate()方法")

        if format.lower() == 'json':
            import json
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.calibration_results, f, indent=2, ensure_ascii=False)

        elif format.lower() == 'csv':
            import pandas as pd
            # 将结果转换为DataFrame格式
            data = []
            for target_name, comparison in self.calibration_results['validation_results']['predicted_vs_target'].items():
                for i, (pred, target, error) in enumerate(zip(
                    comparison['predicted'], comparison['target'], comparison['relative_error']
                )):
                    data.append({
                        'target_name': target_name,
                        'age_group_index': i,
                        'predicted_value': pred,
                        'target_value': target,
                        'relative_error_percent': error
                    })

            df = pd.DataFrame(data)
            df.to_csv(file_path, index=False, encoding='utf-8-sig')

        else:
            raise ValueError(f"不支持的导出格式: {format}")

        print(f"校准结果已导出到: {file_path}")

    def plot_calibration_results(self, save_path: Optional[str] = None):
        """绘制校准结果图表"""
        try:
            import matplotlib.pyplot as plt
            import matplotlib
            matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文
            matplotlib.rcParams['axes.unicode_minus'] = False
        except ImportError:
            print("matplotlib未安装，无法绘制图表")
            return

        if not self.calibration_results:
            raise ValueError("尚未执行校准，请先调用calibrate()方法")

        validation_results = self.calibration_results['validation_results']
        predicted_vs_target = validation_results['predicted_vs_target']

        # 创建子图
        n_targets = len(predicted_vs_target)
        fig, axes = plt.subplots(2, (n_targets + 1) // 2, figsize=(15, 10))
        if n_targets == 1:
            axes = [axes]
        axes = axes.flatten()

        for i, (target_name, comparison) in enumerate(predicted_vs_target.items()):
            ax = axes[i]

            predicted = comparison['predicted']
            target = comparison['target']
            age_groups = [f"{50+j*5}-{54+j*5}" for j in range(len(predicted))]

            x = np.arange(len(age_groups))
            width = 0.35

            ax.bar(x - width/2, target, width, label='目标值', alpha=0.7)
            ax.bar(x + width/2, predicted, width, label='预测值', alpha=0.7)

            ax.set_xlabel('年龄组')
            ax.set_ylabel('值')
            ax.set_title(target_name.replace('_', ' ').title())
            ax.set_xticks(x)
            ax.set_xticklabels(age_groups, rotation=45)
            ax.legend()
            ax.grid(True, alpha=0.3)

        # 隐藏多余的子图
        for i in range(n_targets, len(axes)):
            axes[i].set_visible(False)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"校准结果图表已保存到: {save_path}")
        else:
            plt.show()

        plt.close()