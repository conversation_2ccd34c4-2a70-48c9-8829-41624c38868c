"""
结直肠癌筛查微观模拟模型主类
整合所有模块，提供统一的模拟接口
"""

import random
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import numpy as np
from tqdm import tqdm

from .enums import Gender, SimulationType
from .individual import Individual
from ..modules.population import PopulationModule, PopulationParameters
from ..modules.disease import DiseaseNaturalHistoryModule, DiseaseParameters
from ..modules.screening import ScreeningModule, ScreeningParameters, ScreeningStrategy
from ..modules.economics import EconomicsModule, EconomicParameters
from ..modules.calibration import CalibrationModule, CalibrationParameters
from ..data.data_manager import DataManager


@dataclass
class ModelConfiguration:
    """模型配置"""
    initial_population: int = 100000
    simulation_years: int = 100
    start_year: int = 2020
    simulation_type: SimulationType = SimulationType.NATURAL_POPULATION
    random_seed: Optional[int] = 42

    # 模块参数
    population_params: Optional[PopulationParameters] = None
    disease_params: Optional[DiseaseParameters] = None
    screening_params: Optional[ScreeningParameters] = None
    economics_params: Optional[EconomicParameters] = None
    calibration_params: Optional[CalibrationParameters] = None


class ColorectalCancerMicrosimulationModel:
    """结直肠癌筛查微观模拟模型主类"""

    def __init__(self, initial_population: int = 10000, config: Optional[ModelConfiguration] = None):
        self.config = config or ModelConfiguration(initial_population=initial_population)

        # 设置随机种子
        if self.config.random_seed is not None:
            random.seed(self.config.random_seed)
            np.random.seed(self.config.random_seed)

        # 初始化各模块
        self.population_module = PopulationModule(
            initial_population,
            self.config.population_params or PopulationParameters()
        )

        self.disease_module = DiseaseNaturalHistoryModule(
            self.config.disease_params or DiseaseParameters()
        )

        self.screening_module = ScreeningModule(
            self.config.screening_params or ScreeningParameters()
        )

        self.economics_module = EconomicsModule(
            self.config.economics_params or EconomicParameters()
        )

        self.calibration_module = CalibrationModule(
            self.config.calibration_params or CalibrationParameters()
        )

        self.data_manager = DataManager()

        # 模拟状态
        self.is_initialized = False
        self.current_year = self.config.start_year
        self.simulation_results = {}

        # 创建预定义筛查策略
        self.screening_module.create_predefined_strategies()

    def setup_population(self, age_distribution: Dict[int, float],
                        gender_ratio: float = 0.5) -> None:
        """设置初始人口"""
        print("初始化人口...")

        # 初始化人口
        population = self.population_module.initialize_population(
            age_distribution, gender_ratio
        )

        # 初始化个体风险因素
        print("初始化风险因素...")
        for individual in population:
            self.disease_module.initialize_individual_risk_factors(individual)

        self.is_initialized = True
        print(f"人口初始化完成，共 {len(population)} 个个体")

    def add_screening_strategy(self, strategy: ScreeningStrategy) -> None:
        """添加筛查策略"""
        self.screening_module.add_strategy(strategy)
        print(f"已添加筛查策略: {strategy.name}")

    def run_simulation(self, years: int, screening_strategy: str,
                      show_progress: bool = True) -> Dict[str, Any]:
        """
        运行模拟

        Args:
            years: 模拟年数
            screening_strategy: 筛查策略名称
            show_progress: 是否显示进度条

        Returns:
            模拟结果字典
        """
        if not self.is_initialized:
            raise ValueError("模型未初始化，请先调用setup_population()方法")

        if screening_strategy not in self.screening_module.strategies:
            raise ValueError(f"筛查策略 '{screening_strategy}' 不存在")

        print(f"开始模拟 - 策略: {screening_strategy}, 年数: {years}")

        # 初始化结果存储
        yearly_results = []

        # 获取初始人口
        population = self.population_module.population

        # 模拟循环
        progress_bar = tqdm(range(years), desc="模拟进展") if show_progress else range(years)

        for year_offset in progress_bar:
            current_year = self.config.start_year + year_offset
            self.current_year = current_year

            if show_progress:
                progress_bar.set_description(f"模拟年份: {current_year}")

            # 1. 更新人口（年龄增长和自然死亡）
            alive_population = self.population_module.update_population(current_year)

            # 2. 疾病自然史进展
            for individual in alive_population:
                self.disease_module.progress_disease(individual, current_year)

            # 3. 执行筛查
            screening_stats = self.screening_module.screen_population(
                alive_population, screening_strategy, current_year
            )

            # 4. 收集年度统计数据
            population_stats = self.population_module.get_population_statistics()
            disease_stats = self.disease_module.get_disease_statistics(population)

            yearly_result = {
                'year': current_year,
                'population_stats': population_stats,
                'disease_stats': disease_stats,
                'screening_stats': screening_stats
            }
            yearly_results.append(yearly_result)

        # 5. 经济学评价
        print("计算经济学指标...")
        economics_outcome = self.economics_module.evaluate_strategy(
            screening_strategy, population, years, self.config.start_year
        )

        # 整合最终结果
        final_results = {
            'model_config': {
                'initial_population': self.config.initial_population,
                'simulation_years': years,
                'start_year': self.config.start_year,
                'screening_strategy': screening_strategy
            },
            'yearly_results': yearly_results,
            'final_population_stats': self.population_module.get_population_statistics(),
            'final_disease_stats': self.disease_module.get_disease_statistics(population),
            'screening_summary': self.screening_module.get_screening_statistics(),
            'economics_outcome': {
                'strategy_name': economics_outcome.strategy_name,
                'total_cost': economics_outcome.total_cost,
                'total_screening_cost': economics_outcome.total_screening_cost,
                'total_treatment_cost': economics_outcome.total_treatment_cost,
                'qalys_gained': economics_outcome.qalys_gained,
                'life_years_gained': economics_outcome.life_years_gained,
                'cost_per_qaly_gained': economics_outcome.cost_per_qaly_gained,
                'cost_per_life_year_saved': economics_outcome.cost_per_life_year_saved,
                'net_monetary_benefit': economics_outcome.net_monetary_benefit,
                'cancer_cases_prevented': economics_outcome.cancer_cases_prevented,
                'cancer_deaths_prevented': economics_outcome.cancer_deaths_prevented
            }
        }

        # 保存结果
        self.simulation_results[screening_strategy] = final_results

        print(f"模拟完成 - 策略: {screening_strategy}")
        self._print_simulation_summary(final_results)

        return final_results


    def compare_strategies(self, strategy_names: List[str], years: int,
                          show_progress: bool = True) -> Dict[str, Any]:
        """
        比较多个筛查策略

        Args:
            strategy_names: 要比较的策略名称列表
            years: 模拟年数
            show_progress: 是否显示进度条

        Returns:
            比较结果字典
        """
        if not self.is_initialized:
            raise ValueError("模型未初始化，请先调用setup_population()方法")

        print(f"开始策略比较 - 策略数: {len(strategy_names)}, 年数: {years}")

        comparison_results = {}

        for i, strategy_name in enumerate(strategy_names):
            print(f"\n运行策略 {i+1}/{len(strategy_names)}: {strategy_name}")

            # 为每个策略创建独立的人口副本
            original_population = self.population_module.population.copy()

            try:
                # 运行单个策略模拟
                results = self.run_simulation(years, strategy_name, show_progress)
                comparison_results[strategy_name] = results

            except Exception as e:
                print(f"策略 {strategy_name} 运行失败: {e}")
                comparison_results[strategy_name] = {'error': str(e)}

            finally:
                # 恢复原始人口状态
                self.population_module.population = original_population

        # 执行经济学比较分析
        print("\n执行经济学比较分析...")
        valid_strategies = [name for name, result in comparison_results.items()
                          if 'error' not in result]

        if len(valid_strategies) > 1:
            economics_comparison = self.economics_module.compare_strategies(valid_strategies)

            # 整合比较结果
            final_comparison = {
                'comparison_summary': {
                    'strategies_compared': len(valid_strategies),
                    'simulation_years': years,
                    'comparison_date': __import__('datetime').datetime.now().isoformat()
                },
                'strategy_results': comparison_results,
                'economics_comparison': economics_comparison,
                'recommendations': self._generate_recommendations(economics_comparison)
            }
        else:
            final_comparison = {
                'comparison_summary': {
                    'strategies_compared': len(valid_strategies),
                    'simulation_years': years,
                    'error': '有效策略数量不足，无法进行比较'
                },
                'strategy_results': comparison_results
            }

        print("策略比较完成")
        return final_comparison

    def calibrate_model(self, use_neural_network: bool = True,
                       n_samples: int = 10000) -> Dict[str, Any]:
        """
        校准模型参数

        Args:
            use_neural_network: 是否使用神经网络校准
            n_samples: 拉丁超立方样本数

        Returns:
            校准结果
        """
        print(f"开始模型校准 - 样本数: {n_samples}")

        # 更新校准参数
        self.calibration_module.params.n_samples = n_samples

        # 加载默认校准目标
        self.calibration_module.load_default_targets()

        # 执行校准
        calibration_results = self.calibration_module.calibrate(use_neural_network)

        # 应用校准后的参数到疾病模块
        if 'best_parameters' in calibration_results:
            best_params = calibration_results['best_parameters']
            self._apply_calibrated_parameters(best_params)
            print("校准参数已应用到模型")

        return calibration_results

    def _apply_calibrated_parameters(self, parameters: Dict[str, float]):
        """应用校准后的参数"""
        disease_params = self.disease_module.params

        # 更新疾病参数
        if 'adenoma_generation_a' in parameters:
            disease_params.adenoma_generation_a = parameters['adenoma_generation_a']
        if 'adenoma_generation_b' in parameters:
            disease_params.adenoma_generation_b = parameters['adenoma_generation_b']
        if 'adenoma_generation_c' in parameters:
            disease_params.adenoma_generation_c = parameters['adenoma_generation_c']
        if 'low_to_high_risk_mean' in parameters:
            disease_params.low_to_high_risk_mean = parameters['low_to_high_risk_mean']
        if 'low_to_high_risk_std' in parameters:
            disease_params.low_to_high_risk_std = parameters['low_to_high_risk_std']
        if 'high_to_preclinical_mean' in parameters:
            disease_params.high_to_preclinical_mean = parameters['high_to_preclinical_mean']
        if 'high_to_preclinical_std' in parameters:
            disease_params.high_to_preclinical_std = parameters['high_to_preclinical_std']
        if 'male_adenoma_multiplier' in parameters:
            disease_params.male_adenoma_multiplier = parameters['male_adenoma_multiplier']
        if 'male_progression_multiplier' in parameters:
            disease_params.male_progression_multiplier = parameters['male_progression_multiplier']

    def _print_simulation_summary(self, results: Dict[str, Any]):
        """打印模拟结果摘要"""
        print("\n=== 模拟结果摘要 ===")

        config = results['model_config']
        print(f"策略: {config['screening_strategy']}")
        print(f"人口规模: {config['initial_population']}")
        print(f"模拟年数: {config['simulation_years']}")

        # 人口统计
        pop_stats = results['final_population_stats']
        print(f"\n人口统计:")
        print(f"  存活人口: {pop_stats['alive_population']}")
        print(f"  死亡人口: {pop_stats['dead_population']}")
        print(f"  癌症死亡: {pop_stats['death_causes']['cancer']}")

        # 疾病统计
        disease_stats = results['final_disease_stats']
        print(f"\n疾病统计:")
        print(f"  正常状态: {disease_stats['normal']}")
        print(f"  低风险腺瘤: {disease_stats['low_risk_adenoma']}")
        print(f"  高风险腺瘤: {disease_stats['high_risk_adenoma']}")
        print(f"  临床癌症: {sum([disease_stats[f'clinical_cancer_stage_{i}'] for i in ['i', 'ii', 'iii', 'iv']])}")

        # 筛查统计
        screening_stats = results['screening_summary']
        print(f"\n筛查统计:")
        print(f"  总筛查人数: {screening_stats['total_screened']}")
        print(f"  检出病例: {screening_stats['total_detected']}")
        print(f"  筛查覆盖率: {screening_stats['screening_coverage']:.2%}")
        print(f"  总筛查成本: ¥{screening_stats['total_cost']:,.0f}")

        # 经济学结果
        economics = results['economics_outcome']
        print(f"\n经济学评价:")
        print(f"  总成本: ¥{economics['total_cost']:,.0f}")
        print(f"  QALYs获得: {economics['qalys_gained']:.2f}")
        print(f"  成本/QALY: ¥{economics['cost_per_qaly_gained']:,.0f}")
        print(f"  净货币效益: ¥{economics['net_monetary_benefit']:,.0f}")
        print(f"  预防癌症病例: {economics['cancer_cases_prevented']}")
        print(f"  预防癌症死亡: {economics['cancer_deaths_prevented']}")

    def _generate_recommendations(self, economics_comparison: Dict[str, Dict]) -> List[str]:
        """生成策略推荐"""
        recommendations = []

        # 找出最具成本效益的策略
        cost_effective_strategies = [
            name for name, data in economics_comparison.items()
            if data.get('cost_effective', False) and not data.get('dominated', False)
        ]

        if cost_effective_strategies:
            # 按净货币效益排序
            best_strategy = max(
                cost_effective_strategies,
                key=lambda x: economics_comparison[x].get('net_monetary_benefit', 0)
            )
            recommendations.append(f"推荐策略: {best_strategy} (最高净货币效益)")

        # 找出成本最低的策略
        lowest_cost_strategy = min(
            economics_comparison.keys(),
            key=lambda x: economics_comparison[x].get('total_cost', float('inf'))
        )
        recommendations.append(f"成本最低策略: {lowest_cost_strategy}")

        # 找出效果最好的策略
        highest_effectiveness_strategy = max(
            economics_comparison.keys(),
            key=lambda x: economics_comparison[x].get('total_effectiveness', 0)
        )
        recommendations.append(f"效果最佳策略: {highest_effectiveness_strategy}")

        return recommendations

    def save_model_state(self, filename: str):
        """保存模型状态"""
        model_state = {
            'config': self.config,
            'simulation_results': self.simulation_results,
            'current_year': self.current_year,
            'is_initialized': self.is_initialized
        }

        self.data_manager.file_manager.save_results(model_state, filename, 'pickle')

        # 保存人口数据
        if self.is_initialized:
            self.data_manager.db_manager.save_population(
                self.population_module.population, filename
            )

        print(f"模型状态已保存: {filename}")

    def load_model_state(self, filename: str):
        """加载模型状态"""
        try:
            model_state = self.data_manager.file_manager.load_results(filename, 'pickle')

            self.config = model_state['config']
            self.simulation_results = model_state['simulation_results']
            self.current_year = model_state['current_year']
            self.is_initialized = model_state['is_initialized']

            # 加载人口数据
            if self.is_initialized:
                population = self.data_manager.db_manager.load_population()
                self.population_module.population = population

            print(f"模型状态已加载: {filename}")

        except Exception as e:
            print(f"加载模型状态失败: {e}")

    def export_results(self, format: str = 'csv', output_dir: str = 'exports'):
        """导出所有结果"""
        if not self.simulation_results:
            print("没有可导出的结果")
            return

        import os
        os.makedirs(output_dir, exist_ok=True)

        for strategy_name, results in self.simulation_results.items():
            # 导出年度结果
            yearly_data = results['yearly_results']
            filename = f"{strategy_name}_yearly_results"
            self.data_manager.file_manager.save_results(
                {'yearly_data': yearly_data}, filename, format
            )

            # 导出经济学结果
            economics_data = results['economics_outcome']
            filename = f"{strategy_name}_economics"
            self.data_manager.file_manager.save_results(
                economics_data, filename, format
            )

        print(f"结果已导出到 {output_dir} 目录")

    def plot_results(self, save_path: Optional[str] = None):
        """绘制结果图表"""
        if not self.simulation_results:
            print("没有可绘制的结果")
            return

        try:
            import matplotlib.pyplot as plt
            import matplotlib
            matplotlib.rcParams['font.sans-serif'] = ['SimHei']
            matplotlib.rcParams['axes.unicode_minus'] = False

            # 创建多个子图
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))

            for strategy_name, results in self.simulation_results.items():
                yearly_results = results['yearly_results']
                years = [r['year'] for r in yearly_results]

                # 人口变化
                alive_pop = [r['population_stats']['alive_population'] for r in yearly_results]
                axes[0, 0].plot(years, alive_pop, label=strategy_name)

                # 癌症发病
                cancer_cases = [r['disease_stats']['clinical_cancer_stage_i'] +
                              r['disease_stats']['clinical_cancer_stage_ii'] +
                              r['disease_stats']['clinical_cancer_stage_iii'] +
                              r['disease_stats']['clinical_cancer_stage_iv']
                              for r in yearly_results]
                axes[0, 1].plot(years, cancer_cases, label=strategy_name)

                # 筛查成本累积
                cumulative_cost = []
                total_cost = 0
                for r in yearly_results:
                    total_cost += r['screening_stats']['total_cost']
                    cumulative_cost.append(total_cost)
                axes[1, 0].plot(years, cumulative_cost, label=strategy_name)

                # 检出病例累积
                cumulative_detected = []
                total_detected = 0
                for r in yearly_results:
                    total_detected += r['screening_stats']['detected_cases']
                    cumulative_detected.append(total_detected)
                axes[1, 1].plot(years, cumulative_detected, label=strategy_name)

            # 设置图表标题和标签
            axes[0, 0].set_title('存活人口变化')
            axes[0, 0].set_xlabel('年份')
            axes[0, 0].set_ylabel('人口数')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)

            axes[0, 1].set_title('癌症病例数变化')
            axes[0, 1].set_xlabel('年份')
            axes[0, 1].set_ylabel('病例数')
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)

            axes[1, 0].set_title('累积筛查成本')
            axes[1, 0].set_xlabel('年份')
            axes[1, 0].set_ylabel('成本 (元)')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)

            axes[1, 1].set_title('累积检出病例')
            axes[1, 1].set_xlabel('年份')
            axes[1, 1].set_ylabel('病例数')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)

            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"图表已保存到: {save_path}")
            else:
                plt.show()

            plt.close()

        except ImportError:
            print("matplotlib未安装，无法绘制图表")

    def get_model_summary(self) -> Dict[str, Any]:
        """获取模型摘要信息"""
        summary = {
            'model_info': {
                'version': '1.0.0',
                'initialized': self.is_initialized,
                'current_year': self.current_year,
                'strategies_run': len(self.simulation_results)
            },
            'configuration': {
                'initial_population': self.config.initial_population,
                'start_year': self.config.start_year,
                'simulation_type': self.config.simulation_type.value
            },
            'available_strategies': list(self.screening_module.strategies.keys()),
            'completed_simulations': list(self.simulation_results.keys())
        }

        if self.is_initialized:
            pop_stats = self.population_module.get_population_statistics()
            summary['current_population'] = {
                'total': pop_stats['total_population'],
                'alive': pop_stats['alive_population'],
                'mean_age': pop_stats.get('mean_age', 'N/A')
            }

        return summary