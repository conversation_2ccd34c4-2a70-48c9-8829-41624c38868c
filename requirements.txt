# 结直肠癌筛查微观模拟模型依赖包

# 科学计算
numpy>=1.21.0
scipy>=1.7.0
pandas>=1.3.0

# 机器学习
scikit-learn>=1.0.0
tensorflow>=2.8.0

# 数据可视化
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# 数据处理
openpyxl>=3.0.0
xlsxwriter>=3.0.0

# 配置管理
pyyaml>=6.0
configparser>=5.0.0

# 日志
loguru>=0.6.0

# 进度条
tqdm>=4.60.0

# 统计分析
statsmodels>=0.13.0

# 并行计算
joblib>=1.1.0
multiprocessing-logging>=0.3.0

# 测试
pytest>=7.0.0
pytest-cov>=4.0.0

# 代码质量
black>=22.0.0
flake8>=4.0.0
mypy>=0.950

# 文档
sphinx>=4.0.0
sphinx-rtd-theme>=1.0.0

# Web界面（可选）
flask>=2.0.0
fastapi>=0.75.0
uvicorn>=0.17.0

# 数据库（可选）
sqlalchemy>=1.4.0
sqlite3
