# 1. 初始化模型
model = ColorectalCancerMicrosimulationModel(initial_population=10000)

# 2. 设置人口参数
age_distribution = {50: 0.1, 55: 0.15, 60: 0.2, 65: 0.25, 70: 0.2, 75: 0.1}
model.setup_population(age_distribution)

# 3. 添加筛查策略
strategy = {
    'start_age': 50,
    'end_age': 75,
    'interval': 1,
    'tools': [ScreeningTool.FIT, ScreeningTool.COLONOSCOPY]
}
model.add_screening_strategy("fit_then_colonoscopy", strategy)

# 4. 运行模拟
results = model.run_simulation(years=20, screening_strategy="fit_then_colonoscopy")

# 5. 查看结果
model.plot_results()