"""
基本模拟示例
演示如何使用CCSM模型进行基本的筛查策略模拟
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.ccsm.core.model import ColorectalCancerMicrosimulationModel
from src.ccsm.modules.screening import ScreeningStrategy
from src.ccsm.core.enums import ScreeningTool


def main():
    """主函数"""
    print("=== 结直肠癌筛查微观模拟模型示例 ===\n")

    # 1. 创建模型实例
    print("1. 创建模型实例...")
    model = ColorectalCancerMicrosimulationModel(initial_population=1000)

    # 2. 设置人口分布
    print("2. 设置人口分布...")
    age_distribution = {
        50: 0.2,  # 50岁：20%
        55: 0.3,  # 55岁：30%
        60: 0.3,  # 60岁：30%
        65: 0.2   # 65岁：20%
    }
    gender_ratio = 0.5  # 男女比例1:1

    model.setup_population(age_distribution, gender_ratio)

    # 3. 添加自定义筛查策略
    print("3. 添加自定义筛查策略...")
    custom_strategy = ScreeningStrategy(
        name="custom_fit_strategy",
        start_age=50,
        end_age=70,
        tools=[ScreeningTool.FIT],
        intervals=[2],  # 每2年一次
        sequential=False
    )
    model.add_screening_strategy(custom_strategy)

    # 4. 运行单个策略模拟
    print("4. 运行单个策略模拟...")
    print("   策略：年度FIT筛查")
    results = model.run_simulation(
        years=10,
        screening_strategy="annual_fit",
        show_progress=True
    )

    print("\n--- 模拟结果摘要 ---")
    economics = results['economics_outcome']
    print(f"总成本: ¥{economics['total_cost']:,.0f}")
    print(f"QALYs获得: {economics['qalys_gained']:.2f}")
    print(f"成本/QALY: ¥{economics['cost_per_qaly_gained']:,.0f}")
    print(f"预防癌症病例: {economics['cancer_cases_prevented']}")

    # 5. 比较多个策略
    print("\n5. 比较多个策略...")
    strategies_to_compare = ["annual_fit", "biennial_fit", "custom_fit_strategy"]

    comparison_results = model.compare_strategies(
        strategy_names=strategies_to_compare,
        years=10,
        show_progress=False
    )

    print("\n--- 策略比较结果 ---")
    if 'economics_comparison' in comparison_results:
        economics_comparison = comparison_results['economics_comparison']

        print(f"{'策略名称':<20} {'总成本':<15} {'成本/QALY':<15} {'成本效益':<10}")
        print("-" * 70)

        for strategy_name, data in economics_comparison.items():
            cost = data.get('total_cost', 0)
            cost_per_qaly = data.get('cost_per_qaly', 0)
            cost_effective = "是" if data.get('cost_effective', False) else "否"

            print(f"{strategy_name:<20} ¥{cost:<14,.0f} ¥{cost_per_qaly:<14,.0f} {cost_effective:<10}")

    # 6. 显示推荐
    if 'recommendations' in comparison_results:
        print("\n--- 策略推荐 ---")
        for recommendation in comparison_results['recommendations']:
            print(f"• {recommendation}")

    # 7. 导出结果
    print("\n6. 导出结果...")
    model.export_results(format='csv', output_dir='example_results')

    # 8. 生成图表（如果matplotlib可用）
    print("7. 生成结果图表...")
    try:
        model.plot_results(save_path='example_results/simulation_plots.png')
    except ImportError:
        print("   matplotlib未安装，跳过图表生成")

    print("\n=== 示例完成 ===")
    print("结果文件已保存到 'example_results' 目录")


if __name__ == '__main__':
    main()
