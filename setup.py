"""
结直肠癌筛查微观模拟模型 (CCSM) 安装配置
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "结直肠癌筛查微观模拟模型"

# 读取requirements文件
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return []

setup(
    name="ccsm",
    version="1.0.0",
    author="CCSM Development Team",
    author_email="<EMAIL>",
    description="结直肠癌筛查微观模拟模型",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/example/ccsm",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Topic :: Scientific/Engineering :: Medical Science Apps.",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
            "mypy>=0.950",
        ],
        "docs": [
            "sphinx>=4.0.0",
            "sphinx-rtd-theme>=1.0.0",
        ],
        "web": [
            "flask>=2.0.0",
            "fastapi>=0.75.0",
            "uvicorn>=0.17.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "ccsm=ccsm.ui.cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "ccsm": [
            "data/*.csv",
            "data/*.json",
            "config/*.yaml",
            "config/*.yml",
        ],
    },
    zip_safe=False,
)
