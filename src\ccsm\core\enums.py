"""
CCSM模型枚举类型定义
包含性别、癌症阶段、筛查工具等枚举类型
"""

from enum import Enum, IntEnum


class Gender(IntEnum):
    """性别枚举"""
    MALE = 0
    FEMALE = 1


class CancerStage(IntEnum):
    """癌症阶段枚举"""
    NORMAL = 0                          # 正常状态
    LOW_RISK_ADENOMA = 1               # 低风险腺瘤
    HIGH_RISK_ADENOMA = 2              # 高风险腺瘤
    SMALL_SERRATED_ADENOMA = 3         # 小无蒂锯齿状腺瘤
    LARGE_SERRATED_ADENOMA = 4         # 大无蒂锯齿状腺瘤
    PRECLINICAL_CANCER = 5             # 临床前癌症
    CLINICAL_CANCER_STAGE_I = 6        # 临床癌症I期
    CLINICAL_CANCER_STAGE_II = 7       # 临床癌症II期
    CLINICAL_CANCER_STAGE_III = 8      # 临床癌症III期
    CLINICAL_CANCER_STAGE_IV = 9       # 临床癌症IV期


class ScreeningTool(Enum):
    """筛查工具枚举"""
    FIT = "FIT"                        # 粪便免疫化学检测
    COLONOSCOPY = "Colonoscopy"        # 结肠镜检查
    SIGMOIDOSCOPY = "Sigmoidoscopy"    # 乙状结直肠镜检查
    RISK_QUESTIONNAIRE = "RiskQuestionnaire"  # 风险评估问卷
    OTHER = "Other"                    # 其他筛查工具


class AdenomaLocation(Enum):
    """腺瘤位置枚举"""
    PROXIMAL_COLON = "proximal"        # 近端结肠
    DISTAL_COLON = "distal"           # 远端结肠
    RECTUM = "rectum"                 # 直肠


class DeathCause(Enum):
    """死亡原因枚举"""
    ALIVE = "alive"                   # 存活
    NATURAL = "natural"               # 自然死亡
    CANCER = "cancer"                 # 癌症死亡
    OTHER = "other"                   # 其他原因死亡


class RiskFactor(Enum):
    """风险因素枚举"""
    FAMILY_HISTORY = "family_history"                    # 一级亲属史
    INFLAMMATORY_BOWEL_DISEASE = "inflammatory_bowel_disease"  # 炎性肠病史
    OVERWEIGHT = "overweight"                           # 超重肥胖
    DIABETES = "diabetes"                               # 糖尿病
    SMOKING = "smoking"                                 # 吸烟
    SEDENTARY_LIFESTYLE = "sedentary_lifestyle"         # 静坐生活方式


class SimulationType(Enum):
    """模拟类型枚举"""
    NATURAL_POPULATION = "natural_population"          # 自然人群队列
    BIRTH_COHORT = "birth_cohort"                      # 出生队列
